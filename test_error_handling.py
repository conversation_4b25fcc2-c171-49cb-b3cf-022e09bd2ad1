#!/usr/bin/env python3
"""
Test script for error handling and resilience features

This script tests the new error handling, retry logic, circuit breaker patterns,
and rollback mechanisms implemented in Stellar Sync.
"""

import sys
import os
import logging
import time
from unittest.mock import Mock, patch

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.error_handling import (
    StellarSyncError,
    DiscogsAPIError,
    ShopifyAPIError,
    ValidationError,
    RetryConfig,
    retry_with_backoff,
    CircuitBreaker,
    CircuitBreakerConfig,
    TransactionManager,
    categorize_error,
    ErrorCategory
)
from utils.api_resilience import RateLimiter, RateLimitConfig
import requests

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_retry_decorator():
    """Test the retry decorator functionality"""
    logger.info("Testing retry decorator...")
    
    # Test successful retry after failures
    call_count = 0
    
    @retry_with_backoff(RetryConfig(max_retries=3, base_delay=0.1))
    def flaky_function():
        nonlocal call_count
        call_count += 1
        if call_count < 3:
            raise requests.exceptions.ConnectionError("Simulated connection error")
        return "success"
    
    try:
        result = flaky_function()
        assert result == "success"
        assert call_count == 3
        logger.info("✓ Retry decorator test passed")
    except Exception as e:
        logger.error(f"✗ Retry decorator test failed: {e}")
        return False
    
    return True


def test_circuit_breaker():
    """Test circuit breaker functionality"""
    logger.info("Testing circuit breaker...")
    
    config = CircuitBreakerConfig(failure_threshold=2, recovery_timeout=1)
    circuit_breaker = CircuitBreaker(config)
    
    def failing_function():
        raise Exception("Simulated failure")
    
    def successful_function():
        return "success"
    
    try:
        # Test failures that should open the circuit
        for i in range(3):
            try:
                circuit_breaker.call(failing_function)
            except Exception:
                pass
        
        # Circuit should be open now
        try:
            circuit_breaker.call(failing_function)
            logger.error("✗ Circuit breaker should be open")
            return False
        except Exception as e:
            if "Circuit breaker is OPEN" not in str(e):
                logger.error(f"✗ Unexpected exception: {e}")
                return False
        
        # Wait for recovery timeout
        time.sleep(1.1)
        
        # Should be able to call again (half-open state)
        result = circuit_breaker.call(successful_function)
        assert result == "success"
        
        logger.info("✓ Circuit breaker test passed")
        return True
        
    except Exception as e:
        logger.error(f"✗ Circuit breaker test failed: {e}")
        return False


def test_transaction_manager():
    """Test transaction manager and rollback functionality"""
    logger.info("Testing transaction manager...")
    
    transaction_manager = TransactionManager()
    rollback_executed = []
    
    def rollback_action_1():
        rollback_executed.append("action_1")
    
    def rollback_action_2():
        rollback_executed.append("action_2")
    
    try:
        # Add rollback actions
        transaction_manager.add_rollback_action(rollback_action_1, description="Test action 1")
        transaction_manager.add_rollback_action(rollback_action_2, description="Test action 2")
        
        # Execute rollback
        success = transaction_manager.rollback()
        
        # Check that rollback actions were executed in reverse order (LIFO)
        assert success == True
        assert rollback_executed == ["action_2", "action_1"]
        
        logger.info("✓ Transaction manager test passed")
        return True
        
    except Exception as e:
        logger.error(f"✗ Transaction manager test failed: {e}")
        return False


def test_rate_limiter():
    """Test rate limiter functionality"""
    logger.info("Testing rate limiter...")
    
    config = RateLimitConfig(requests_per_second=2, requests_per_minute=5)
    rate_limiter = RateLimiter(config)
    
    try:
        start_time = time.time()
        
        # Make requests that should trigger rate limiting
        for i in range(3):
            rate_limiter.wait_if_needed()
        
        elapsed_time = time.time() - start_time
        
        # Should have taken at least 1 second due to rate limiting
        if elapsed_time < 0.5:
            logger.warning(f"Rate limiter may not be working correctly. Elapsed time: {elapsed_time:.2f}s")
        
        logger.info("✓ Rate limiter test passed")
        return True
        
    except Exception as e:
        logger.error(f"✗ Rate limiter test failed: {e}")
        return False


def test_error_categorization():
    """Test error categorization functionality"""
    logger.info("Testing error categorization...")
    
    try:
        # Test network error
        network_error = requests.exceptions.ConnectionError("Connection failed")
        category = categorize_error(network_error)
        assert category == ErrorCategory.NETWORK_ERROR
        
        # Test timeout error
        timeout_error = requests.exceptions.Timeout("Request timed out")
        category = categorize_error(timeout_error)
        assert category == ErrorCategory.TIMEOUT
        
        # Test HTTP errors with mock response
        mock_response = Mock()
        mock_response.status_code = 401
        http_error = requests.exceptions.HTTPError("Unauthorized")
        category = categorize_error(http_error, mock_response)
        assert category == ErrorCategory.AUTHENTICATION
        
        mock_response.status_code = 429
        category = categorize_error(http_error, mock_response)
        assert category == ErrorCategory.RATE_LIMIT
        
        mock_response.status_code = 500
        category = categorize_error(http_error, mock_response)
        assert category == ErrorCategory.SERVER_ERROR
        
        # Test validation error
        validation_error = ValidationError("Invalid input")
        category = categorize_error(validation_error)
        assert category == ErrorCategory.VALIDATION
        
        logger.info("✓ Error categorization test passed")
        return True
        
    except Exception as e:
        logger.error(f"✗ Error categorization test failed: {e}")
        return False


def test_stellar_sync_errors():
    """Test StellarSync custom error classes"""
    logger.info("Testing StellarSync error classes...")
    
    try:
        # Test base StellarSyncError
        error = StellarSyncError(
            "Test error",
            category=ErrorCategory.NETWORK_ERROR,
            details={"test": "data"},
            original_exception=Exception("Original")
        )
        
        error_dict = error.to_dict()
        assert error_dict["message"] == "Test error"
        assert error_dict["category"] == "network_error"
        assert error_dict["details"]["test"] == "data"
        assert "timestamp" in error_dict
        
        # Test DiscogsAPIError
        discogs_error = DiscogsAPIError("Discogs API failed")
        assert isinstance(discogs_error, StellarSyncError)
        
        # Test ShopifyAPIError
        shopify_error = ShopifyAPIError("Shopify API failed")
        assert isinstance(shopify_error, StellarSyncError)
        
        # Test ValidationError
        validation_error = ValidationError("Invalid data")
        assert isinstance(validation_error, StellarSyncError)
        
        logger.info("✓ StellarSync error classes test passed")
        return True
        
    except Exception as e:
        logger.error(f"✗ StellarSync error classes test failed: {e}")
        return False


def run_all_tests():
    """Run all error handling tests"""
    logger.info("Starting error handling and resilience tests...")
    
    tests = [
        test_retry_decorator,
        test_circuit_breaker,
        test_transaction_manager,
        test_rate_limiter,
        test_error_categorization,
        test_stellar_sync_errors
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            logger.error(f"Test {test.__name__} crashed: {e}")
            failed += 1
    
    logger.info(f"\nTest Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        logger.info("🎉 All error handling tests passed!")
        return True
    else:
        logger.error(f"❌ {failed} tests failed")
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
