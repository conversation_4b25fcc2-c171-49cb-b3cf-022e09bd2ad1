# Use an official Python runtime as a parent image
FROM python:3.11.5-slim

# Set the working directory to /app
WORKDIR /app

# Copy the current directory contents into the container at /app
COPY . /app

# Install any needed packages specified in requirements.txt
RUN pip install --trusted-host pypi.python.org -r requirements.txt

# Create directories for persistent data
RUN mkdir -p /app/instance /app/logs

# Make port 5000 available to the world outside this container
EXPOSE 5000

# Define environment variables
ENV FLASK_APP=api.py
ENV INSTANCE_PATH=/app/instance
ENV LOGS_PATH=/app/logs

# Database connection string (default is empty, should be provided at runtime)
ENV DATABASE_URL=""

# Discogs API Endpoint for creating a new listing
ENV DISCOGS_API_URL=""
ENV DISCOGS_TOKEN=""

# Shopify API information
ENV SHOPIFY_TOKEN=""
ENV SHOPIFY_SHOP_NAME=""

# Create a startup script
COPY start.sh /app/start.sh
RUN chmod +x /app/start.sh

# Run the startup script when the container launches
CMD ["/app/start.sh"]
