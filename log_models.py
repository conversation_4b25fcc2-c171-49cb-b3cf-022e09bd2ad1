from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
import enum

# Use the same db instance from models.py
from models import db

class LogLevel(enum.Enum):
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    DEBUG = "DEBUG"

class LogPlatform(enum.Enum):
    DISCOGS = "discogs"
    SHOPIFY = "shopify"
    BOTH = "both"
    SYSTEM = "system"

class ListingLog(db.Model):
    """Model for storing listing creation logs"""
    id = db.Column(db.Integer, primary_key=True)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)
    level = db.Column(db.Enum(LogLevel), default=LogLevel.INFO)
    
    # Listing details
    release_id = db.Column(db.String(50), nullable=False)
    artist = db.Column(db.String(255), nullable=False)
    title = db.Column(db.String(255), nullable=False)
    price = db.Column(db.Float, nullable=False)
    condition = db.Column(db.String(50), nullable=False)
    platform = db.Column(db.Enum(LogPlatform), nullable=False)
    
    # Platform-specific IDs
    discogs_listing_id = db.Column(db.String(50), nullable=True)
    shopify_product_id = db.Column(db.String(50), nullable=True)
    
    def __repr__(self):
        return f"<ListingLog {self.timestamp} - {self.platform.value} - {self.artist} - {self.title}>"
    
    def to_dict(self):
        """Convert the log entry to a dictionary"""
        return {
            "id": self.id,
            "timestamp": self.timestamp.isoformat(),
            "level": self.level.value,
            "release_id": self.release_id,
            "artist": self.artist,
            "title": self.title,
            "price": self.price,
            "condition": self.condition,
            "platform": self.platform.value,
            "discogs_listing_id": self.discogs_listing_id,
            "shopify_product_id": self.shopify_product_id
        }
    
    def to_log_string(self):
        """Convert the log entry to a string format similar to the original log format"""
        message = f"{self.timestamp.strftime('%Y-%m-%d %H:%M:%S')} - listings - {self.level.value} - "
        message += f"NEW LISTING: [{self.platform.value.upper()}] Release ID: {self.release_id} | "
        message += f"Artist: {self.artist} | Title: {self.title} | Price: ${self.price} | Condition: {self.condition}"
        
        if self.discogs_listing_id:
            message += f" | Discogs Listing ID: {self.discogs_listing_id}"
        if self.shopify_product_id:
            message += f" | Shopify Product ID: {self.shopify_product_id}"
            
        return message

class ErrorLog(db.Model):
    """Model for storing error logs"""
    id = db.Column(db.Integer, primary_key=True)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)
    level = db.Column(db.Enum(LogLevel), default=LogLevel.ERROR)
    
    # Error details
    error_type = db.Column(db.String(100), nullable=False)
    details = db.Column(db.Text, nullable=False)
    release_id = db.Column(db.String(50), nullable=True)
    
    def __repr__(self):
        return f"<ErrorLog {self.timestamp} - {self.error_type}>"
    
    def to_dict(self):
        """Convert the log entry to a dictionary"""
        return {
            "id": self.id,
            "timestamp": self.timestamp.isoformat(),
            "level": self.level.value,
            "error_type": self.error_type,
            "details": self.details,
            "release_id": self.release_id
        }
    
    def to_log_string(self):
        """Convert the log entry to a string format similar to the original log format"""
        message = f"{self.timestamp.strftime('%Y-%m-%d %H:%M:%S')} - errors - {self.level.value} - "
        message += f"ERROR: {self.error_type}"
        if self.release_id:
            message += f" | Release ID: {self.release_id}"
        message += f" | Details: {self.details}"
            
        return message
