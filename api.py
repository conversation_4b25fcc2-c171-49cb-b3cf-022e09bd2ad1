from flask import Flask, render_template, request, jsonify
import discogs_shopify
from discogs_shopify import shopify_client
from third_parties.discogs import DiscogsListing, Condition
from utils.logging_utils import get_recent_listings
from utils.error_handling import (
    StellarSyncError,
    ValidationError,
    create_error_response,
    log_error_with_context
)
import os
import re
import logging
from sqlalchemy import create_engine
from sqlalchemy.dialects.postgresql.base import PGDialect

# Monkey patch the PostgreSQL dialect to handle CockroachDB version string
def _get_server_version_info(self, connection):
    v = connection.exec_driver_sql("SELECT version()").scalar()
    m = re.match(r"CockroachDB.*v(\d+)\.(\d+)\.(\d+)", v)
    if m:
        return (int(m.group(1)), int(m.group(2)), int(m.group(3)))
    else:
        # If we can't parse the version, return a default
        return (19, 2, 0)  # Default to a compatible version

# Apply the monkey patch
PGDialect._get_server_version_info = _get_server_version_info

from models import db
import log_models  # Import to register models

app = Flask(__name__)

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configure the database
# Get the database URL from environment variables
database_url = os.getenv('DATABASE_URL')
if not database_url:
    raise ValueError("DATABASE_URL environment variable is not set. Please provide a valid database connection string.")

# Ensure we're using the lowercase database name
if 'DB_NAME_LOWER' in os.environ:
    # Extract parts of the connection string
    if '@' in database_url and '/' in database_url:
        prefix = database_url.split('@')[0] + '@' + database_url.split('@')[1].split('/')[0] + '/'
        suffix = ''
        if '?' in database_url:
            suffix = '?' + database_url.split('?')[1]

        # Reconstruct with lowercase database name
        database_url = prefix + os.environ['DB_NAME_LOWER'] + suffix
        print(f"Using database URL: {database_url}")

app.config['SQLALCHEMY_DATABASE_URI'] = database_url
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Initialize the database
db.init_app(app)

# Create the database tables
with app.app_context():
    db.create_all()

def get_version():
    """Read version from .version file"""
    try:
        with open('.version', 'r') as f:
            return f.read().strip()
    except Exception:
        return "Unknown"


@app.route("/")
def home():
    # Populate data for template
    selected_collections_data = shopify_client.get_custom_collections()
    selected_collections_data = [
        {"name": item["title"], "value": item["id"]}
        for item in selected_collections_data
    ]
    condition_data = [condition.to_dict() for condition in Condition]
    condition_data = [item["value"] for item in condition_data]

    return render_template(
        "create_listings_v1.html",
        selected_collections_data=selected_collections_data,
        condition_data=condition_data,
        version=get_version(),
    )


@app.route("/listings/recent", methods=["GET"])
def recent_listings():
    """Get recent listings from the log"""
    try:
        count = request.args.get("count", default=20, type=int)
        listings = get_recent_listings(count)
        return jsonify({"listings": listings})
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@app.route("/listings/log")
def listings_log():
    """View listings log page"""
    return render_template("listings_log.html", version=get_version())


@app.route("/create_discog_and_shopify_listings", methods=["POST"])
def create_listings():
    """Create new discogs and shopify listings with comprehensive error handling"""
    try:
        data = request.form
        logger.info(f"Received listing creation request")

        # Input validation
        try:
            # Map data
            create_on_discogs = "create_on_discogs" in data

            # Define a list of required fields
            required_fields = [
                "selected_collections",
                "discogs_release_id",
                "media_condition",
                "sleeve_condition",
                "price",
                "quantity",
                "product_condition",
                "media_type",
                "channels",
                "speed",
            ]

            # Add fields that are only required if create_on_discogs is checked
            if create_on_discogs:
                required_fields.extend(["comments", "bin_location"])

            # Check if any required field is missing in the data
            missing_fields = [field for field in required_fields if field not in data]
            if missing_fields:
                error_message = f"Missing required fields: {', '.join(missing_fields)}"
                raise ValidationError(error_message)

            # Handle multiple selected collections
            selected_collections = request.form.getlist("selected_collections")

            # Validate and parse input data
            discogs_release_id = data.get("discogs_release_id")
            if not discogs_release_id or not discogs_release_id.isdigit():
                raise ValidationError("Invalid Discogs release ID")

            media_condition = data.get("media_condition")
            sleeve_condition = data.get("sleeve_condition")

            try:
                price_float = float(data.get("price"))
                if price_float <= 0:
                    raise ValidationError("Price must be greater than 0")
            except (ValueError, TypeError):
                raise ValidationError("Invalid price format")

            try:
                quantity = int(data.get("quantity"))
                if quantity <= 0 or quantity > 100:
                    raise ValidationError("Quantity must be between 1 and 100")
            except (ValueError, TypeError):
                raise ValidationError("Invalid quantity format")

            comments = data.get("comments", "")
            bin_location = data.get("bin_location", "")
            description = data.get("description", "")
            product_condition = data.get("product_condition", "")
            barcode = data.get("barcode", "")
            product_status = data.get("product_status", "active")
            media_type = data.get("media_type", "")
            channels = data.get("channels", "Stereo")
            speed = data.get("speed", "N/A")
            release_year = data.get("release_year", "")

            # Validate release year if provided
            if release_year and not re.match(r'^(19|20)\d{2}$', release_year):
                raise ValidationError("Invalid release year format")

            logger.info(f"Creating listings for release {discogs_release_id}")
            logger.debug(f"Product status: {product_status}, Release year: {release_year}")

        except ValidationError as e:
            logger.warning(f"Validation error: {str(e)}")
            return jsonify(create_error_response(e)), 400

        # Create DiscogsListing instance
        try:
            new_listing = DiscogsListing(
                release_id=discogs_release_id,
                condition=media_condition,
                sleeve_condition=sleeve_condition,
                price=price_float,
                comments=comments,
                status="For Sale",
                location=bin_location,
            )
        except Exception as e:
            error = ValidationError(f"Invalid listing data: {str(e)}")
            logger.error(f"Failed to create DiscogsListing: {str(e)}")
            return jsonify(create_error_response(error)), 400

        # Create the listings
        try:
            (
                discog_listing_ids,
                shopify_url,
            ) = discogs_shopify.create_discog_and_shopify_listings(
                new_listing=new_listing,
                create_on_discogs=create_on_discogs,
                selected_collections=selected_collections,
                quantity=quantity,
                description=description,
                product_condition=product_condition,
                barcode=barcode,
                product_status=product_status,
                media_type=media_type,
                channels=channels,
                speed=speed,
                release_year=release_year,
            )

            logger.info(f"Successfully created listings for release {discogs_release_id}")

            return jsonify({
                "success": True,
                "message": "Listings created successfully",
                "data": {
                    "shopify_url": shopify_url,
                    "discog_listing_ids": discog_listing_ids,
                    "release_id": discogs_release_id
                }
            })

        except StellarSyncError as e:
            logger.error(f"Business logic error: {str(e)}")
            log_error_with_context(e, logger)

            # Return appropriate HTTP status code based on error category
            status_code = 503 if e.category.value in ['network_error', 'timeout'] else 400
            return jsonify(create_error_response(e, include_details=True)), status_code

    except Exception as e:
        # Catch-all for unexpected errors
        logger.error(f"Unexpected error in create_listings: {str(e)}")
        error = StellarSyncError(f"An unexpected error occurred: {str(e)}", original_exception=e)
        log_error_with_context(error, logger)

        return jsonify(create_error_response(error)), 500


@app.route("/shopify/custom_collections", methods=["GET"])
def get_custom_collections():
    """Get the custom shopify collections"""
    try:
        logger.debug("Fetching Shopify custom collections")
        custom_collections = shopify_client.get_custom_collections()

        return jsonify({
            "success": True,
            "data": custom_collections
        })

    except StellarSyncError as e:
        logger.error(f"Error fetching custom collections: {str(e)}")
        log_error_with_context(e, logger)
        return jsonify(create_error_response(e)), 503

    except Exception as e:
        logger.error(f"Unexpected error fetching custom collections: {str(e)}")
        error = StellarSyncError(f"Failed to fetch custom collections: {str(e)}", original_exception=e)
        return jsonify(create_error_response(error)), 500





@app.route("/discogs/conditions", methods=["GET"])
def get_conditions():
    """Get a list of discog media conditions"""
    conditions_list = [condition.to_dict() for condition in Condition]
    return jsonify(conditions_list)


@app.route("/health", methods=["GET"])
def health_check():
    """Health check endpoint"""
    try:
        from datetime import datetime

        health_status = {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "services": {
                "discogs": discogs_shopify.discogs_client.health_check(),
                "shopify": discogs_shopify.shopify_client.health_check(),
                "database": True  # Assume healthy if we can respond
            }
        }

        # Check if all services are healthy
        all_healthy = all(health_status["services"].values())

        if all_healthy:
            return jsonify(health_status), 200
        else:
            health_status["status"] = "degraded"
            return jsonify(health_status), 503

    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return jsonify({
            "status": "unhealthy",
            "error": str(e)
        }), 503


if __name__ == "__main__":
    app.run(host="0.0.0.0", debug=discogs_shopify.DEBUG)
