<!DOCTYPE html>
<html>
<head>
    <title>Stellar Records - Create Listings</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/css/materialize.min.css">
</head>
<body class="grey darken-4">
    <style>
        /* Dark mode styles */
        body {
            font-family: 'Roboto', sans-serif;
            display: flex;
            min-height: 100vh;
            flex-direction: column;
        }

        main {
            flex: 1 0 auto;
        }

        .container {
            background-color: #424242;
            padding: 20px 30px 30px 30px;
            border-radius: 8px;
            margin-top: 20px;
            margin-bottom: 20px;
            color: #f5f5f5;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }

        h3, h4, h5 {
            color: #f5f5f5;
            font-weight: 300;
        }

        .card {
            background-color: #616161;
            color: #f5f5f5;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        .card:hover {
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
            transform: translateY(-2px);
        }

        .card .card-title {
            color: #f5f5f5;
            font-weight: 400;
        }

        .card .card-content {
            color: #f5f5f5;
            padding: 20px;
        }

        .btn {
            background-color: #26a69a;
            margin: 5px;
            border-radius: 4px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        .btn:hover {
            background-color: #2bbbad;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
            transform: translateY(-1px);
        }

        /* Form styles */
        .input-field label {
            color: #bdbdbd !important;
        }

        .input-field input[type=text],
        .input-field input[type=number] {
            color: #f5f5f5 !important;
            border-bottom: 1px solid #9e9e9e !important;
        }

        .input-field textarea.materialize-textarea {
            color: #f5f5f5 !important;
            min-height: 100px !important;
        }

        .input-field input[type=text]:focus,
        .input-field input[type=number]:focus {
            border-bottom: 1px solid #26a69a !important;
            box-shadow: 0 1px 0 0 #26a69a !important;
        }

        .input-field textarea.materialize-textarea:focus {
            border: 1px solid #26a69a !important;
            box-shadow: 0 0 5px rgba(38, 166, 154, 0.5) !important;
        }

        /* Dropdown and select styles */
        .dropdown-content {
            background-color: #616161;
        }

        .dropdown-content li > a,
        .dropdown-content li > span {
            color: #f5f5f5 !important;
        }

        .select-wrapper input.select-dropdown {
            color: #f5f5f5 !important;
        }

        .select-wrapper .caret {
            fill: #f5f5f5 !important;
        }

        /* Checkbox styles */
        [type="checkbox"]+span:not(.lever) {
            color: #f5f5f5;
        }

        [type="checkbox"].filled-in:checked+span:not(.lever):after {
            border: 2px solid #26a69a;
            background-color: #26a69a;
        }

        /* Spinner */
        .spinner {
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 4px solid #26a69a;
            width: 24px;
            height: 24px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
            display: none;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Response container */
        #response-container {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: #424242;
            border: 1px solid #616161;
            border-radius: 8px;
            padding: 25px;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.5);
            z-index: 9999;
            display: none;
            color: #f5f5f5;
            min-width: 300px;
            max-width: 500px;
        }

        #response-container a {
            color: #26a69a;
            text-decoration: underline;
        }

        #ok-button {
            background-color: #26a69a;
            margin-top: 15px;
        }

        #ok-button:hover {
            background-color: #2bbbad;
        }

        /* Textarea specific styles */
        textarea.materialize-textarea {
            overflow-y: auto !important;
            resize: vertical !important;
            border: 1px solid #616161 !important;
            border-radius: 4px !important;
            padding: 8px !important;
            background-color: #333333 !important;
        }

        /* Form section styles */
        .form-section {
            border-bottom: 1px solid #616161;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }

        .form-section-title {
            color: #26a69a;
            font-size: 1.2rem;
            margin-bottom: 15px;
            font-weight: 400;
        }

        /* Footer */
        .footer {
            margin-top: 20px;
            margin-bottom: 20px;
            color: #9e9e9e;
            font-size: 12px;
        }
    </style>
    <script>
        document.addEventListener("DOMContentLoaded", function() {
            const form = document.querySelector("form");
            const submitButton = document.getElementById("submit-btn");
            const spinner = document.getElementById("spinner");
            const okButton = document.getElementById("ok-button");

            form.addEventListener("submit", async function(event) {
                event.preventDefault();

                // Show the spinner, disable the submit button
                spinner.style.display = "block";
                submitButton.disabled = true;

                // Submit the form using AJAX (Assuming you're using the fetch API)
                const formData = new FormData(form);
                try {
                    const response = await fetch("/create_discog_and_shopify_listings", {
                        method: "POST",
                        body: formData
                    });

                    if (response.ok) {
                        // Successful response
                        const data = await response.json();

                        // Display the success message in the response-container
                        const responseContainer = document.getElementById("response-container");
                        responseContainer.style.display = "block";

                        let discogListingsDisplay = "No Discog Listing IDs available"; // Default message if discog_listing_ids is null or empty

                        if (data.discog_listing_ids && data.discog_listing_ids.length > 0) {
                            // Check if discog_listing_ids exists and has at least one item
                            discogListingsDisplay = "Discog Listing IDs: " + data.discog_listing_ids.join(", ");
                        }

                        responseContainer.innerHTML = `
                            <p>${data.message}</p>
                            <p>Shopify URL: <a href="${data.shopify_url}">${data.shopify_url}</a></p>
                            <p>${discogListingsDisplay}</p>
                            <!-- OK button to close the container and clear the form -->
                            <button id="ok-button" class="btn waves-effect waves-light">OK</button>
                        `;

                        // Add a click event listener to the OK button
                        const okButton = document.getElementById("ok-button");
                        okButton.addEventListener("click", function() {
                            // Hide the response container
                            responseContainer.style.display = "none";

                            // Clear the form fields
                            form.reset();

                            // Re-enable the submit button
                            submitButton.disabled = false;
                        });
                    }
                    else {
                        // Error response
                        const errorData = await response.json();

                        // Display the error message in the response-container
                        const responseContainer = document.getElementById("response-container");
                        responseContainer.style.display = "block";
                        responseContainer.innerHTML = `
                            <p>Error: ${errorData.error}</p>
                            <!-- OK button to close the container  -->
                            <button id="ok-button" class="btn waves-effect waves-light">OK</button>
                        `;

                        // Add a click event listener to the OK button
                        const okButton = document.getElementById("ok-button");
                        okButton.addEventListener("click", function() {
                            // Hide the response container
                            responseContainer.style.display = "none";

                            // Clear the form fields
                            form.reset();

                            // Re-enable the submit button
                            submitButton.disabled = false;
                        });
                    }
                } catch (error) {
                    console.error("An error occurred:", error);
                } finally {
                    // Hide the spinner, enable the submit button
                    spinner.style.display = "none";
                    submitButton.disabled = false;
                }
            });
        });
    </script>

    <div class="center-align" style="margin-top: 20px;">
        <img src="https://stellarrecordsfl.com/cdn/shop/files/1C6BC6FD-204A-44A6-9FCE-B1B9A8BFD25F.png?v=1683606444&width=80">
    </div>
    <div class="center-align" style="margin-top: 10px; margin-bottom: 10px;">
        <a href="/listings/log" class="btn waves-effect waves-light">
            <i class="material-icons left">list</i>Listings Log
        </a>
    </div>

    <main>
        <div class="container">
            <h3 class="center-align">Create Listings</h3>

            <form method="POST" action="/create_discog_and_shopify_listings">
                <!-- Basic Information Section -->
                <div class="form-section">
                    <div class="form-section-title">
                        <i class="material-icons left">album</i>Basic Information
                    </div>

                    <div class="row">
                        <div class="input-field col s12 m6">
                            <input id="discogs_release_id" type="number" class="validate" name="discogs_release_id" required>
                            <label for="discogs_release_id">Discogs Release ID</label>
                        </div>
                        <div class="input-field col s12 m6">
                            <div style="margin-top: 15px;">
                                <label>
                                    <input type="checkbox" id="create_on_discogs" name="create_on_discogs" class="filled-in">
                                    <span>Create New Listing(s) on Discogs</span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Product Details Section -->
                <div class="form-section">
                    <div class="form-section-title">
                        <i class="material-icons left">info</i>Product Details
                    </div>

                    <div class="row">
                        <div class="input-field col s12 m6">
                            <select id="product_condition" name="product_condition" required>
                                <option value="" disabled selected>Select Product Condition</option>
                                <option value="New">New</option>
                                <option value="Used">Used</option>
                            </select>
                            <label>Product Condition</label>
                        </div>
                        <div class="input-field col s12 m6">
                            <input id="barcode" type="text" class="validate" name="barcode">
                            <label for="barcode">Barcode (UPC/EAN)</label>
                        </div>
                    </div>

                    <div class="row">
                        <div class="input-field col s12">
                            <select id="product_status" name="product_status" required>
                                <option value="active" selected>Active</option>
                                <option value="draft">Draft</option>
                            </select>
                            <label>Product Status</label>
                        </div>
                    </div>

                    <div class="row">
                        <div class="input-field col s12">
                            <textarea id="description" class="materialize-textarea" name="description" rows="8" style="min-height: 150px;"></textarea>
                            <label for="description">Product Description (Tracklist will be appended automatically)</label>
                        </div>
                    </div>
                </div>

                <!-- Discogs Details Section -->
                <div class="form-section" id="discogs-details-section" style="display: none !important;">
                    <div class="form-section-title">
                        <i class="material-icons left">album</i>Discogs Details
                    </div>

                    <div class="row">
                        <div class="input-field col s12 m6">
                            <select id="bin_location" name="bin_location" class="validate">
                                <option value="" disabled selected>Select Bin Location</option>
                                <option value="Alt & Indy">Alt & Indy</option>
                                <option value="Blues & Jazz">Blues & Jazz</option>
                                <option value="Cassettes">Cassettes</option>
                                <option value="CDs">CDs</option>
                                <option value="Country & Folk">Country & Folk</option>
                                <option value="Electronic">Electronic</option>
                                <option value="Hip-hop">Hip-hop</option>
                                <option value="Punk & Metal">Punk & Metal</option>
                                <option value="Novelty & Holiday">Novelty & Holiday</option>
                                <option value="Pop & Rock">Pop & Rock</option>
                                <option value="RnB & Soul">RnB & Soul</option>
                                <option value="Soundtracks">Soundtracks</option>
                                <option value="Glass">Glass</option>
                            </select>
                            <label>Bin Location</label>
                        </div>
                        <div class="input-field col s12 m6">
                            <textarea id="comments" class="materialize-textarea" name="comments" rows="5" style="min-height: 100px;"></textarea>
                            <label for="comments">Comments</label>
                        </div>
                    </div>
                </div>

                <!-- Media Condition Section -->
                <div class="form-section">
                    <div class="form-section-title">
                        <i class="material-icons left">music_note</i>Media Details
                    </div>

                    <div class="row">
                        <div class="input-field col s12 m6">
                            <select id="media_condition" name="media_condition" required>
                                <option value="" disabled selected>Select Media Condition</option>
                                {% for item in condition_data %}
                                    <option value="{{ item }}">{{ item }}</option>
                                {% endfor %}
                            </select>
                            <label>Media Condition</label>
                        </div>
                        <div class="input-field col s12 m6">
                            <select id="sleeve_condition" name="sleeve_condition" required>
                                <option value="" disabled selected>Select Sleeve Condition</option>
                                {% for item in condition_data %}
                                    <option value="{{ item }}">{{ item }}</option>
                                {% endfor %}
                            </select>
                            <label>Sleeve Condition</label>
                        </div>
                    </div>
                </div>

                <!-- Inventory Section -->
                <div class="form-section">
                    <div class="form-section-title">
                        <i class="material-icons left">inventory</i>Inventory & Pricing
                    </div>

                    <div class="row">
                        <div class="input-field col s12 m6">
                            <input id="price" type="number" class="validate" name="price" step="any" required>
                            <label for="price">Price</label>
                        </div>
                        <div class="input-field col s12 m6">
                            <input id="quantity" type="number" class="validate" name="quantity" min="1" value="1" required>
                            <label for="quantity">Quantity</label>
                        </div>
                    </div>

                    <div class="row">
                        <div class="input-field col s12">
                            <select id="selected_collections" name="selected_collections" multiple required>
                                {% for item in selected_collections_data %}
                                    <option value="{{ item.value }}">{{ item.name }}</option>
                                {% endfor %}
                            </select>
                            <label>Shopify Collections</label>
                        </div>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="row">
                    <div class="col s12">
                        <div class="center-align">
                            <div id="spinner" class="spinner"></div>
                            <button class="btn waves-effect waves-light btn-large" type="submit" name="action" id="submit-btn">
                                <i class="material-icons left">send</i>Create Listing
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </main>
      <!-- response container-->
        <div id="response-container"></div>

        <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>
        <script>
             // Wait for DOM to be fully loaded
             document.addEventListener('DOMContentLoaded', function() {
                 M.AutoInit(); // Initialize Materialize components

             // Initialize textareas with proper height
             var descriptionTextarea = document.getElementById('description');
             var commentsTextarea = document.getElementById('comments');
             if (descriptionTextarea) M.textareaAutoResize(descriptionTextarea);
             if (commentsTextarea) M.textareaAutoResize(commentsTextarea);

             // Toggle Discogs Details section based on checkbox
             const createOnDiscogsCheckbox = document.getElementById("create_on_discogs");
             const discogsDetailsSection = document.getElementById("discogs-details-section");
             const binLocationField = document.getElementById("bin_location");
             const commentsField = document.getElementById("comments");

             // Function to toggle sections visibility and required fields
             function toggleSections() {
                 console.log("Toggle function called, checkbox state:", createOnDiscogsCheckbox.checked);

                 if (createOnDiscogsCheckbox.checked) {
                     // Show the section - use !important to override any CSS
                     discogsDetailsSection.style.cssText = "display: block !important;";
                     console.log("Showing Discogs Details section");

                     // Make fields required
                     binLocationField.setAttribute("required", "");

                     // Add validation class
                     binLocationField.classList.add("validate");

                     // Re-initialize the comments textarea if it's now visible
                     if (commentsField) {
                         setTimeout(function() {
                             M.textareaAutoResize(commentsField);
                         }, 100);
                     }
                 } else {
                     // Hide the section - use !important to override any CSS
                     discogsDetailsSection.style.cssText = "display: none !important;";
                     console.log("Hiding Discogs Details section");

                     // Remove required attribute
                     binLocationField.removeAttribute("required");

                     // Keep validation class for styling
                     binLocationField.classList.add("validate");
                 }
             }

             // Function to force show/hide the section
             function forceToggleSection(show) {
                 if (show) {
                     discogsDetailsSection.style.cssText = "display: block !important;";
                     discogsDetailsSection.classList.remove('hidden');
                     discogsDetailsSection.classList.add('visible');
                 } else {
                     discogsDetailsSection.style.cssText = "display: none !important;";
                     discogsDetailsSection.classList.remove('visible');
                     discogsDetailsSection.classList.add('hidden');
                 }
             }

             // Initial toggle based on checkbox state
             toggleSections();

             // Force toggle based on initial checkbox state
             forceToggleSection(createOnDiscogsCheckbox.checked);

             // Add event listener for checkbox changes
             createOnDiscogsCheckbox.addEventListener("change", function() {
                 console.log("Checkbox changed:", this.checked);
                 toggleSections();
                 forceToggleSection(this.checked);
             });

             // Also add a click event listener as a backup
             createOnDiscogsCheckbox.addEventListener("click", function() {
                 console.log("Checkbox clicked:", this.checked);
                 setTimeout(function() {
                     toggleSections();
                     forceToggleSection(createOnDiscogsCheckbox.checked);
                 }, 50);
             });

             // Add form submission logging
             document.querySelector("form").addEventListener("submit", function(event) {
                 // Log form data
                 console.log("Form submission:");
                 console.log("- Product status:", document.getElementById("product_status").value);

                 // Continue with form submission
                 console.log("Submitting form...");
             });

             }); // Close DOMContentLoaded event listener
        </script>

        <footer class="page-footer grey darken-4" style="padding-top: 0;">
            <div class="footer-copyright">
                <div class="container center-align" style="padding: 10px 0;">
                    <span style="color: #9e9e9e; font-size: 12px;">© 2025 Stellar Records. All rights reserved.</span>
                    <br>
                    <span style="color: #9e9e9e; font-size: 12px;">Version: {{ version }}</span>
                </div>
            </div>
        </footer>

</body>
</html>
