import os
import re
from datetime import datetime
from flask import Flask
from sqlalchemy.dialects.postgresql.base import PGDialect

# Monkey patch the PostgreSQL dialect to handle CockroachDB version string
def _get_server_version_info(self, connection):
    v = connection.exec_driver_sql("SELECT version()").scalar()
    m = re.match(r"CockroachDB.*v(\d+)\.(\d+)\.(\d+)", v)
    if m:
        return (int(m.group(1)), int(m.group(2)), int(m.group(3)))
    else:
        # If we can't parse the version, return a default
        return (19, 2, 0)  # Default to a compatible version

# Apply the monkey patch
PGDialect._get_server_version_info = _get_server_version_info

from log_models import db, ListingLog, ErrorLog, LogLevel, LogPlatform

# Create a Flask app for database access
app = Flask(__name__)

# Get the database URL from environment variables
database_url = os.getenv('DATABASE_URL')
if not database_url:
    raise ValueError("DATABASE_URL environment variable is not set. Please provide a valid database connection string.")

# Ensure we're using the lowercase database name
if 'DB_NAME_LOWER' in os.environ:
    # Extract parts of the connection string
    if '@' in database_url and '/' in database_url:
        prefix = database_url.split('@')[0] + '@' + database_url.split('@')[1].split('/')[0] + '/'
        suffix = ''
        if '?' in database_url:
            suffix = '?' + database_url.split('?')[1]

        # Reconstruct with lowercase database name
        database_url = prefix + os.environ['DB_NAME_LOWER'] + suffix
        print(f"Using database URL: {database_url}")

app.config['SQLALCHEMY_DATABASE_URI'] = database_url
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
db.init_app(app)

# Regular expressions for parsing log entries
LISTING_LOG_PATTERN = re.compile(
    r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) - listings - (\w+) - NEW LISTING: \[(\w+)\] Release ID: (\d+) \| '
    r'Artist: (.*?) \| Title: (.*?) \| Price: \$([\d.]+) \| Condition: (.*?)(?:\s\|\sDiscogs Listing ID: (.*?))?(?:\s\|\sShopify Product ID: (.*?))?$'
)

ERROR_LOG_PATTERN = re.compile(
    r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) - errors - (\w+) - ERROR: (.*?)(?:\s\|\sRelease ID: (\d+))?\s\|\sDetails: (.*?)$'
)

def parse_timestamp(timestamp_str):
    """Parse timestamp string to datetime object"""
    return datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')

def import_listings_log(log_file):
    """Import listings log file into database"""
    count = 0
    try:
        with open(log_file, 'r') as f:
            for line in f:
                line = line.strip()
                match = LISTING_LOG_PATTERN.match(line)
                if match:
                    timestamp_str, level, platform, release_id, artist, title, price, condition, listing_id, product_id = match.groups()

                    # Create a new ListingLog entry
                    log_entry = ListingLog(
                        timestamp=parse_timestamp(timestamp_str),
                        level=LogLevel(level),
                        platform=LogPlatform(platform.lower()),
                        release_id=release_id,
                        artist=artist,
                        title=title,
                        price=float(price),
                        condition=condition,
                        discogs_listing_id=listing_id,
                        shopify_product_id=product_id
                    )

                    # Add to database
                    db.session.add(log_entry)
                    count += 1

                    # Commit every 100 entries to avoid memory issues
                    if count % 100 == 0:
                        db.session.commit()
                        print(f"Imported {count} listing log entries...")

        # Commit any remaining entries
        db.session.commit()
        print(f"Successfully imported {count} listing log entries.")
        return count
    except Exception as e:
        db.session.rollback()
        print(f"Error importing listings log: {str(e)}")
        return 0

def import_errors_log(log_file):
    """Import errors log file into database"""
    count = 0
    try:
        with open(log_file, 'r') as f:
            for line in f:
                line = line.strip()
                match = ERROR_LOG_PATTERN.match(line)
                if match:
                    timestamp_str, level, error_type, release_id, details = match.groups()

                    # Create a new ErrorLog entry
                    log_entry = ErrorLog(
                        timestamp=parse_timestamp(timestamp_str),
                        level=LogLevel(level),
                        error_type=error_type,
                        release_id=release_id,
                        details=details
                    )

                    # Add to database
                    db.session.add(log_entry)
                    count += 1

                    # Commit every 100 entries to avoid memory issues
                    if count % 100 == 0:
                        db.session.commit()
                        print(f"Imported {count} error log entries...")

        # Commit any remaining entries
        db.session.commit()
        print(f"Successfully imported {count} error log entries.")
        return count
    except Exception as e:
        db.session.rollback()
        print(f"Error importing errors log: {str(e)}")
        return 0

def main():
    """Main function to import logs"""
    # Get logs directory
    logs_dir = os.getenv('LOGS_PATH', os.path.join(os.path.dirname(os.path.abspath(__file__)), 'logs'))

    # Check if logs directory exists
    if not os.path.exists(logs_dir):
        print(f"Logs directory '{logs_dir}' does not exist.")
        return

    # Import listings log
    listings_log_file = os.path.join(logs_dir, 'listings.log')
    if os.path.exists(listings_log_file):
        with app.app_context():
            import_listings_log(listings_log_file)
    else:
        print(f"Listings log file '{listings_log_file}' does not exist.")

    # Import errors log
    errors_log_file = os.path.join(logs_dir, 'errors.log')
    if os.path.exists(errors_log_file):
        with app.app_context():
            import_errors_log(errors_log_file)
    else:
        print(f"Errors log file '{errors_log_file}' does not exist.")

if __name__ == "__main__":
    main()
