# Error Handling & Resilience Implementation

This document describes the comprehensive error handling and resilience features implemented in Stellar Sync.

## Overview

The error handling system provides:
- **Retry Logic**: Automatic retry with exponential backoff for transient failures
- **Circuit Breaker Pattern**: Protection against cascading failures
- **Transaction Management**: Rollback capabilities for partial failures
- **Rate Limiting**: Protection against API rate limits
- **Structured Error Handling**: Categorized errors with appropriate responses
- **Health Checks**: Service health monitoring

## Components

### 1. Error Handling (`utils/error_handling.py`)

#### Custom Exception Classes
- `StellarSyncError`: Base exception with categorization and context
- `DiscogsAPIError`: Discogs-specific errors
- `ShopifyAPIError`: Shopify-specific errors
- `ValidationError`: Input validation errors
- `RollbackError`: Rollback operation errors

#### Error Categories
- `NETWORK_ERROR`: Connection issues
- `RATE_LIMIT`: API rate limiting
- `AUTHENTICATION`: Auth failures
- `VALIDATION`: Input validation errors
- `SERVER_ERROR`: 5xx HTTP errors
- `CLIENT_ERROR`: 4xx HTTP errors
- `TIMEOUT`: Request timeouts
- `UNKNOWN`: Uncategorized errors

#### Retry Decorator
```python
@retry_with_backoff(RetryConfig(max_retries=3, base_delay=1.0))
def api_call():
    # Your API call here
    pass
```

#### Circuit Breaker
```python
circuit_breaker = CircuitBreaker(CircuitBreakerConfig(
    failure_threshold=5,
    recovery_timeout=60
))

result = circuit_breaker.call(your_function)
```

#### Transaction Manager
```python
transaction_manager = TransactionManager()

# Add rollback actions
transaction_manager.add_rollback_action(
    action=delete_resource,
    args=(resource_id,),
    description="Delete created resource"
)

# On failure, rollback all actions
transaction_manager.rollback()
```

### 2. API Resilience (`utils/api_resilience.py`)

#### Rate Limiter
- Thread-safe implementation
- Per-second and per-minute limits
- Automatic request throttling

#### Resilient HTTP Client
- Connection pooling
- Automatic retries
- Timeout handling
- Health checks

#### Configuration
```python
rate_limit_config = RateLimitConfig(
    requests_per_minute=60,
    requests_per_second=2,
    burst_limit=5
)

connection_config = ConnectionConfig(
    timeout=30,
    connect_timeout=10,
    read_timeout=20,
    max_retries=3
)
```

### 3. Enhanced API Clients

#### Discogs Client Improvements
- Retry logic on all API methods
- Circuit breaker protection
- Rate limiting (60 requests/minute)
- Structured error handling
- Health check endpoint

#### Shopify Client Improvements
- Retry logic on all API methods
- Circuit breaker protection
- Rate limiting (40 requests/minute)
- Structured error handling
- Health check endpoint

### 4. Transaction Management

#### Main Listing Creation Function
The `create_discog_and_shopify_listings` function now includes:

1. **Step-by-step execution** with progress tracking
2. **Rollback actions** for each successful operation
3. **Automatic cleanup** on failure
4. **Comprehensive logging** of all operations
5. **Error categorization** and appropriate responses

#### Rollback Operations
- **Shopify Product Deletion**: Marks products for deletion
- **Discogs Listing Deletion**: Removes created listings
- **LIFO Execution**: Rollback actions execute in reverse order

### 5. API Endpoint Improvements

#### Enhanced Error Responses
```json
{
  "success": false,
  "error": {
    "message": "Failed to create listings",
    "category": "network_error",
    "timestamp": "2024-01-01T12:00:00Z",
    "details": {
      "context": "Shopify API request",
      "status_code": 503
    }
  }
}
```

#### Health Check Endpoint
```
GET /health
```

Returns service health status:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00Z",
  "services": {
    "discogs": true,
    "shopify": true,
    "database": true
  }
}
```

## Usage Examples

### Basic Error Handling
```python
try:
    result = discogs_client.get_release(release_id)
except DiscogsAPIError as e:
    if e.category == ErrorCategory.RATE_LIMIT:
        # Handle rate limiting
        time.sleep(60)
        retry_operation()
    elif e.category == ErrorCategory.NETWORK_ERROR:
        # Handle network issues
        log_error_for_retry(e)
    else:
        # Handle other errors
        raise e
```

### Transaction with Rollback
```python
transaction_manager = TransactionManager()

try:
    # Create Shopify product
    product = shopify_client.create_product(product_data)
    transaction_manager.add_rollback_action(
        delete_shopify_product, 
        (product['id'],),
        "Delete Shopify product"
    )
    
    # Create Discogs listing
    listing = discogs_client.create_listing(listing_data)
    transaction_manager.add_rollback_action(
        delete_discogs_listing,
        (listing['listing_id'],),
        "Delete Discogs listing"
    )
    
    # Success - clear rollback actions
    transaction_manager.clear()
    
except Exception as e:
    # Failure - execute rollback
    transaction_manager.rollback()
    raise e
```

## Configuration

### Environment Variables
- `RETRY_MAX_ATTEMPTS`: Default retry attempts (default: 3)
- `RETRY_BASE_DELAY`: Base delay for retries (default: 1.0)
- `CIRCUIT_BREAKER_THRESHOLD`: Failure threshold (default: 5)
- `RATE_LIMIT_PER_MINUTE`: Requests per minute (default: 60 for Discogs, 40 for Shopify)

### Logging Configuration
The system uses structured logging with different levels:
- `DEBUG`: Detailed operation information
- `INFO`: General operation status
- `WARNING`: Recoverable issues (retries, rate limiting)
- `ERROR`: Serious errors requiring attention

## Testing

Run the error handling tests:
```bash
python test_error_handling.py
```

This tests:
- Retry decorator functionality
- Circuit breaker behavior
- Transaction manager rollback
- Rate limiter operation
- Error categorization
- Custom exception classes

## Monitoring

### Health Checks
- **Endpoint**: `GET /health`
- **Frequency**: Check every 30 seconds
- **Alerts**: Set up alerts for degraded/unhealthy status

### Error Metrics
Monitor these key metrics:
- Error rate by category
- Retry success rate
- Circuit breaker state changes
- Transaction rollback frequency
- API response times

### Logging
All errors are logged with:
- Error category and details
- Request context
- Stack traces for debugging
- Correlation IDs for tracking

## Best Practices

1. **Always use transaction managers** for multi-step operations
2. **Set appropriate retry limits** to avoid infinite loops
3. **Monitor circuit breaker states** for service health
4. **Use structured logging** for better debugging
5. **Implement health checks** for all external dependencies
6. **Set up alerts** for error rate thresholds
7. **Test rollback scenarios** regularly

## Future Improvements

- **Async/Await Support**: For better performance
- **Distributed Circuit Breakers**: For multi-instance deployments
- **Advanced Metrics**: Prometheus/Grafana integration
- **Correlation IDs**: For request tracing
- **Chaos Engineering**: Automated failure testing
