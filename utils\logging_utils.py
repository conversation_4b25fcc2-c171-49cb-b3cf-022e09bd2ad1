import logging
import os
from datetime import datetime
from flask import current_app
from log_models import db, ListingLog, ErrorLog, LogLevel, LogPlatform
import traceback

# Create logs directory if it doesn't exist (for backward compatibility and console logging)
# Use environment variable for logs directory to support Docker volume mounting
logs_dir = os.getenv('LOGS_PATH', os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'logs'))
if not os.path.exists(logs_dir):
    os.makedirs(logs_dir, exist_ok=True)

# Configure the main logger for console output
def setup_logger(name, level=logging.INFO):
    """Function to set up a logger with console handler"""
    # Create formatter
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    # Create console handler
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)

    # Get logger
    logger = logging.getLogger(name)
    logger.setLevel(level)

    # Add handlers if they don't exist already
    if not logger.handlers:
        logger.addHandler(console_handler)

    return logger

# Create loggers for console output
listings_logger = setup_logger('listings')
error_logger = setup_logger('errors', level=logging.ERROR)

def log_new_listing(release_id, artist, title, price, condition, platform, listing_id=None, product_id=None):
    """
    Log a new listing creation to the database

    Args:
        release_id: Discogs release ID
        artist: Artist name
        title: Release title
        price: Listing price
        condition: Item condition
        platform: 'discogs', 'shopify', or 'both'
        listing_id: Discogs listing ID (optional)
        product_id: Shopify product ID (optional)
    """
    try:
        # Create a new ListingLog entry
        log_entry = ListingLog(
            release_id=str(release_id),
            artist=artist,
            title=title,
            price=float(price),
            condition=condition,
            platform=LogPlatform(platform),
            discogs_listing_id=listing_id,
            shopify_product_id=product_id
        )

        # Add to database
        with current_app.app_context():
            db.session.add(log_entry)
            db.session.commit()

        # Also log to console for debugging
        message = log_entry.to_log_string()
        listings_logger.info(message)

        return message
    except Exception as e:
        error_message = f"Failed to log listing: {str(e)}\n{traceback.format_exc()}"
        error_logger.error(error_message)
        # Try to log the error to the database
        log_error("Logging Error", error_message, release_id)
        return None

def log_error(error_type, details, release_id=None):
    """
    Log an error to the database

    Args:
        error_type: Type of error
        details: Error details
        release_id: Discogs release ID (optional)
    """
    try:
        # Create a new ErrorLog entry
        log_entry = ErrorLog(
            error_type=error_type,
            details=details,
            release_id=str(release_id) if release_id else None
        )

        # Add to database
        with current_app.app_context():
            db.session.add(log_entry)
            db.session.commit()

        # Also log to console for debugging
        message = log_entry.to_log_string()
        error_logger.error(message)

        return message
    except Exception as e:
        # If we can't log to the database, at least log to the console
        error_message = f"Failed to log error: {str(e)}\n{traceback.format_exc()}"
        error_logger.error(error_message)
        return None

def get_recent_listings(count=10):
    """
    Get the most recent listings from the database

    Args:
        count: Number of recent listings to retrieve

    Returns:
        List of recent listing log entries as strings
    """
    try:
        with current_app.app_context():
            # Query the most recent listings
            recent_logs = ListingLog.query.order_by(ListingLog.timestamp.desc()).limit(count).all()

            # Convert to log strings
            return [log.to_log_string() for log in recent_logs]
    except Exception as e:
        error_message = f"Error retrieving recent listings: {str(e)}\n{traceback.format_exc()}"
        error_logger.error(error_message)
        return []
