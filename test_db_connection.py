#!/usr/bin/env python3
import os
import sys
import re
import psycopg2

def test_connection():
    """Test the database connection"""
    # Get the database URL from environment variables
    database_url = os.getenv('DATABASE_URL')
    if not database_url:
        print("ERROR: DATABASE_URL environment variable is not set.")
        return False

    # Get the database name
    db_name = os.getenv('DB_NAME', 'stellarsyncQA')
    db_name_lower = os.getenv('DB_NAME_LOWER', db_name.lower())

    # Ensure SSL mode is set to require
    if 'sslmode=' in database_url:
        database_url = re.sub(r'sslmode=[^&]+', 'sslmode=require', database_url)
    else:
        if '?' in database_url:
            database_url += '&sslmode=require'
        else:
            database_url += '?sslmode=require'

    print(f"Testing connection to database...")
    print(f"Database URL: {database_url}")
    print(f"Database name: {db_name}")
    print(f"Database name (lowercase): {db_name_lower}")

    try:
        # Connect to the database
        conn = psycopg2.connect(database_url)
        cursor = conn.cursor()

        # Check if we can execute a simple query
        cursor.execute("SELECT current_database()")
        result = cursor.fetchone()
        if result:
            print(f"Successfully connected to database: {result[0]}")

            # Check if we can create a test table
            try:
                cursor.execute("CREATE TABLE IF NOT EXISTS connection_test (id SERIAL PRIMARY KEY, test_value TEXT)")
                print("Successfully created test table.")

                # Insert a test value
                cursor.execute("INSERT INTO connection_test (test_value) VALUES ('test')")
                conn.commit()
                print("Successfully inserted test data.")

                # Query the test table
                cursor.execute("SELECT * FROM connection_test")
                rows = cursor.fetchall()
                print(f"Retrieved {len(rows)} rows from test table.")

                # Clean up
                cursor.execute("DROP TABLE connection_test")
                conn.commit()
                print("Successfully cleaned up test table.")
            except Exception as e:
                print(f"Error during table operations: {str(e)}")
                conn.rollback()

        cursor.close()
        conn.close()
        return True
    except Exception as e:
        print(f"Error connecting to database: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_connection()
    sys.exit(0 if success else 1)
