<!DOCTYPE html>
<html>
<head>
    <title>Stellar Records - Listings Log</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto+Mono:wght@300;400&display=swap">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/css/materialize.min.css">
</head>
<body class="grey darken-4">
    <style>
        /* Dark mode styles */
        body {
            font-family: 'Roboto', sans-serif;
            display: flex;
            min-height: 100vh;
            flex-direction: column;
        }

        main {
            flex: 1 0 auto;
        }

        .container {
            background-color: #424242;
            padding: 20px 30px 30px 30px;
            border-radius: 8px;
            margin-top: 20px;
            margin-bottom: 20px;
            color: #f5f5f5;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }

        h3, h4, h5 {
            color: #f5f5f5;
            font-weight: 300;
        }

        .btn {
            background-color: #26a69a;
            margin: 5px;
            border-radius: 4px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        .btn:hover {
            background-color: #2bbbad;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
            transform: translateY(-1px);
        }

        /* Spinner */
        .spinner {
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 4px solid #26a69a;
            width: 24px;
            height: 24px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
            display: none;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Log entry styles */
        .log-entry {
            padding: 15px;
            margin-bottom: 15px;
            border-radius: 8px;
            background-color: #616161;
            border-left: 4px solid #26a69a;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        .log-entry:hover {
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
            transform: translateY(-2px);
        }

        .log-entry pre {
            white-space: pre-wrap;
            word-wrap: break-word;
            color: #f5f5f5;
            margin: 0;
            padding: 0;
            background-color: transparent;
            border: none;
            font-family: 'Roboto Mono', monospace;
            font-size: 14px;
            line-height: 1.5;
        }

        /* Log actions bar */
        .log-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding: 10px 0;
            border-bottom: 1px solid #616161;
        }

        .log-filter {
            display: flex;
            align-items: center;
            background-color: #616161;
            padding: 0 15px;
            border-radius: 25px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        }

        .log-filter label {
            margin-right: 10px;
            color: #f5f5f5;
        }

        .log-filter input {
            color: #f5f5f5;
            border-bottom: none !important;
            box-shadow: none !important;
            margin-bottom: 0;
            height: 36px;
        }

        .log-filter i {
            color: #26a69a;
        }

        /* Highlight for search matches */
        .highlight {
            background-color: #26a69a;
            color: white;
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: 500;
        }

        /* No results message */
        .no-results {
            text-align: center;
            padding: 30px;
            color: #f5f5f5;
            background-color: #616161;
            border-radius: 8px;
            margin-top: 20px;
        }

        /* Footer */
        .footer {
            margin-top: 20px;
            margin-bottom: 20px;
            color: #9e9e9e;
            font-size: 12px;
        }
    </style>

    <div class="center-align" style="margin-top: 20px;">
        <img src="https://stellarrecordsfl.com/cdn/shop/files/1C6BC6FD-204A-44A6-9FCE-B1B9A8BFD25F.png?v=1683606444&width=80">
    </div>

    <main>
        <div class="container">
            <h3 class="center-align">Listings Log</h3>

            <div class="log-actions">
                <a href="/" class="btn waves-effect waves-light">
                    <i class="material-icons left">arrow_back</i>Back to Listings
                </a>
                <div class="log-filter">
                    <i class="material-icons">search</i>
                    <input type="text" id="filter-input" placeholder="Filter by release ID, artist, or title">
                </div>
                <button id="refresh-btn" class="btn waves-effect waves-light">
                    <i class="material-icons left">refresh</i>Refresh
                </button>
            </div>

            <div id="spinner" class="spinner"></div>
            <div id="message" class="center-align" style="margin: 15px 0; padding: 10px; border-radius: 4px; display: none;"></div>

            <div id="log-entries-container">
                <!-- Log entries will be loaded here -->
            </div>
        </div>
    </main>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Load log entries
            loadLogEntries();

            // Set up event listeners
            document.getElementById('refresh-btn').addEventListener('click', loadLogEntries);
            document.getElementById('filter-input').addEventListener('input', filterLogEntries);
        });

        function loadLogEntries() {
            const spinner = document.getElementById('spinner');
            const container = document.getElementById('log-entries-container');

            spinner.style.display = 'block';

            fetch('/listings/recent?count=100')
                .then(response => response.json())
                .then(data => {
                    spinner.style.display = 'none';

                    if (!data.listings || data.listings.length === 0) {
                        container.innerHTML = '<div class="no-results">No log entries found</div>';
                        return;
                    }

                    // Store the original log entries for filtering
                    window.logEntries = data.listings;

                    // Display the log entries
                    displayLogEntries(data.listings);

                    // Apply any existing filter
                    const filterInput = document.getElementById('filter-input');
                    if (filterInput.value.trim()) {
                        filterLogEntries();
                    }
                })
                .catch(error => {
                    spinner.style.display = 'none';
                    showMessage(`Error loading log entries: ${error}`, 'error');
                });
        }

        function displayLogEntries(entries) {
            const container = document.getElementById('log-entries-container');
            container.innerHTML = '';

            entries.forEach(entry => {
                const logEntry = document.createElement('div');
                logEntry.className = 'log-entry';

                const pre = document.createElement('pre');
                pre.textContent = entry;

                logEntry.appendChild(pre);
                container.appendChild(logEntry);
            });
        }

        function filterLogEntries() {
            const filterValue = document.getElementById('filter-input').value.trim().toLowerCase();

            if (!filterValue) {
                // If no filter, display all entries
                displayLogEntries(window.logEntries);
                return;
            }

            // Filter the log entries
            const filteredEntries = window.logEntries.filter(entry =>
                entry.toLowerCase().includes(filterValue)
            );

            // Display the filtered entries
            if (filteredEntries.length === 0) {
                document.getElementById('log-entries-container').innerHTML =
                    '<div class="no-results">No matching log entries found</div>';
            } else {
                displayLogEntries(filteredEntries);

                // Highlight the matching text
                highlightMatches(filterValue);
            }
        }

        function highlightMatches(filterValue) {
            const logEntries = document.querySelectorAll('.log-entry pre');

            logEntries.forEach(pre => {
                const text = pre.textContent;
                const regex = new RegExp(filterValue, 'gi');

                pre.innerHTML = text.replace(regex, match =>
                    `<span class="highlight">${match}</span>`
                );
            });
        }

        function showMessage(message, type) {
            const messageElement = document.getElementById('message');
            messageElement.textContent = message;
            messageElement.style.display = 'block';
            messageElement.className = type === 'error' ? 'red-text' : 'green-text';

            setTimeout(() => {
                messageElement.style.display = 'none';
            }, 5000);
        }
    </script>

    <footer class="page-footer grey darken-4" style="padding-top: 0;">
        <div class="footer-copyright">
            <div class="container center-align" style="padding: 10px 0;">
                <span style="color: #9e9e9e; font-size: 12px;">© 2025 Stellar Records. All rights reserved.</span>
                <br>
                <span style="color: #9e9e9e; font-size: 12px;">Version: {{ version }}</span>
            </div>
        </div>
    </footer>
</body>
</html>
