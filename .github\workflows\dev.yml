name: Build and Push Docker Image

on:
  push:
    branches:
      - dev

jobs:
  build-and-push:
    runs-on: ubuntu-latest

    env:
      IMAGE_NAME: roachandroll/stellarsync

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USER }}
          password: ${{ secrets.DOCKER_PASS }}

      - name: Generate version number
        id: genver
        run: |
          DATE=$(date +%Y.%m.%d)
          RUN_NUMBER=$(echo $GITHUB_RUN_NUMBER)
          VERSION="$DATE.$RUN_NUMBER"
          echo "VERSION=$VERSION" >> $GITHUB_ENV
          echo "Generated version: $VERSION"

      - name: Create .version file
        run: |
          echo "$VERSION" > .version
          echo "Created .version file with content: $VERSION"

      - name: Build and tag Docker image
        run: |
          docker build \
            -t $IMAGE_NAME:devlatest \
            .

      - name: Verify .version file in the image
        run: |
          echo "Verifying .version file in the Docker image..."
          docker run --rm $IMAGE_NAME:devlatest cat /app/.version

      - name: Push Docker images
        run: |
          docker push $IMAGE_NAME:devlatest