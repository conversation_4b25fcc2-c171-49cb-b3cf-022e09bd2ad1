"""
API resilience utilities for Stellar Sync

This module provides rate limiting, connection pooling, timeout handling,
and health check mechanisms for API operations.
"""

import time
import logging
import threading
from typing import Dict, Optional, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
from collections import defaultdict, deque
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

from .error_handling import (
    StellarSyncError, 
    ErrorCategory, 
    RetryConfig, 
    retry_with_backoff,
    handle_api_error
)


@dataclass
class RateLimitConfig:
    """Configuration for rate limiting"""
    requests_per_minute: int = 60
    requests_per_second: int = 2
    burst_limit: int = 5


@dataclass
class ConnectionConfig:
    """Configuration for HTTP connections"""
    timeout: int = 30
    connect_timeout: int = 10
    read_timeout: int = 20
    max_retries: int = 3
    backoff_factor: float = 0.3
    pool_connections: int = 10
    pool_maxsize: int = 10


class RateLimiter:
    """Thread-safe rate limiter implementation"""
    
    def __init__(self, config: RateLimitConfig):
        self.config = config
        self.requests_per_minute = deque()
        self.requests_per_second = deque()
        self.lock = threading.Lock()
        self.logger = logging.getLogger(__name__)
    
    def wait_if_needed(self):
        """Wait if rate limit would be exceeded"""
        with self.lock:
            now = datetime.now()
            
            # Clean old requests from tracking
            self._clean_old_requests(now)
            
            # Check per-second limit
            if len(self.requests_per_second) >= self.config.requests_per_second:
                sleep_time = 1.0 - (now - self.requests_per_second[0]).total_seconds()
                if sleep_time > 0:
                    self.logger.debug(f"Rate limit: sleeping {sleep_time:.2f}s for per-second limit")
                    time.sleep(sleep_time)
                    now = datetime.now()
                    self._clean_old_requests(now)
            
            # Check per-minute limit
            if len(self.requests_per_minute) >= self.config.requests_per_minute:
                sleep_time = 60.0 - (now - self.requests_per_minute[0]).total_seconds()
                if sleep_time > 0:
                    self.logger.debug(f"Rate limit: sleeping {sleep_time:.2f}s for per-minute limit")
                    time.sleep(sleep_time)
                    now = datetime.now()
                    self._clean_old_requests(now)
            
            # Record this request
            self.requests_per_second.append(now)
            self.requests_per_minute.append(now)
    
    def _clean_old_requests(self, now: datetime):
        """Remove old request timestamps"""
        # Remove requests older than 1 second
        while self.requests_per_second and (now - self.requests_per_second[0]).total_seconds() >= 1.0:
            self.requests_per_second.popleft()
        
        # Remove requests older than 1 minute
        while self.requests_per_minute and (now - self.requests_per_minute[0]).total_seconds() >= 60.0:
            self.requests_per_minute.popleft()


class HealthChecker:
    """Health check mechanism for API endpoints"""
    
    def __init__(self):
        self.health_status: Dict[str, Dict] = defaultdict(lambda: {
            "is_healthy": True,
            "last_check": None,
            "consecutive_failures": 0,
            "last_error": None
        })
        self.lock = threading.Lock()
        self.logger = logging.getLogger(__name__)
    
    def check_health(self, service_name: str, health_check_func: callable) -> bool:
        """
        Check health of a service
        
        Args:
            service_name: Name of the service to check
            health_check_func: Function that returns True if service is healthy
        
        Returns:
            True if service is healthy
        """
        with self.lock:
            try:
                is_healthy = health_check_func()
                self._update_health_status(service_name, is_healthy, None)
                return is_healthy
            except Exception as e:
                self._update_health_status(service_name, False, str(e))
                return False
    
    def _update_health_status(self, service_name: str, is_healthy: bool, error: Optional[str]):
        """Update health status for a service"""
        status = self.health_status[service_name]
        status["last_check"] = datetime.now()
        
        if is_healthy:
            status["is_healthy"] = True
            status["consecutive_failures"] = 0
            status["last_error"] = None
            if status["consecutive_failures"] > 0:
                self.logger.info(f"Service {service_name} is healthy again")
        else:
            status["is_healthy"] = False
            status["consecutive_failures"] += 1
            status["last_error"] = error
            self.logger.warning(f"Service {service_name} health check failed (failure #{status['consecutive_failures']}): {error}")
    
    def is_healthy(self, service_name: str) -> bool:
        """Check if a service is currently healthy"""
        with self.lock:
            return self.health_status[service_name]["is_healthy"]
    
    def get_health_status(self, service_name: str) -> Dict:
        """Get detailed health status for a service"""
        with self.lock:
            return dict(self.health_status[service_name])


class ResilientHTTPClient:
    """HTTP client with resilience features"""
    
    def __init__(self, 
                 base_url: str,
                 rate_limit_config: Optional[RateLimitConfig] = None,
                 connection_config: Optional[ConnectionConfig] = None,
                 retry_config: Optional[RetryConfig] = None,
                 service_name: str = "unknown"):
        
        self.base_url = base_url.rstrip('/')
        self.service_name = service_name
        self.logger = logging.getLogger(__name__)
        
        # Initialize configurations
        self.rate_limit_config = rate_limit_config or RateLimitConfig()
        self.connection_config = connection_config or ConnectionConfig()
        self.retry_config = retry_config or RetryConfig()
        
        # Initialize components
        self.rate_limiter = RateLimiter(self.rate_limit_config)
        self.health_checker = HealthChecker()
        
        # Create session with connection pooling and retry strategy
        self.session = self._create_session()
    
    def _create_session(self) -> requests.Session:
        """Create a requests session with retry strategy and connection pooling"""
        session = requests.Session()
        
        # Configure retry strategy
        retry_strategy = Retry(
            total=self.connection_config.max_retries,
            backoff_factor=self.connection_config.backoff_factor,
            status_forcelist=[429, 500, 502, 503, 504],
            method_whitelist=["HEAD", "GET", "OPTIONS", "POST", "PUT", "DELETE"]
        )
        
        # Configure HTTP adapter with connection pooling
        adapter = HTTPAdapter(
            max_retries=retry_strategy,
            pool_connections=self.connection_config.pool_connections,
            pool_maxsize=self.connection_config.pool_maxsize
        )
        
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        return session
    
    def request(self, method: str, endpoint: str, **kwargs) -> requests.Response:
        """
        Make a resilient HTTP request
        
        Args:
            method: HTTP method
            endpoint: API endpoint (relative to base_url)
            **kwargs: Additional arguments for requests
        
        Returns:
            requests.Response object
        
        Raises:
            StellarSyncError: On API errors
        """
        # Apply rate limiting
        self.rate_limiter.wait_if_needed()
        
        # Prepare URL
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        
        # Set default timeout if not provided
        if 'timeout' not in kwargs:
            kwargs['timeout'] = (
                self.connection_config.connect_timeout,
                self.connection_config.read_timeout
            )
        
        try:
            self.logger.debug(f"Making {method} request to {url}")
            response = self.session.request(method, url, **kwargs)
            
            # Check for HTTP errors
            response.raise_for_status()
            
            # Update health status
            self.health_checker._update_health_status(self.service_name, True, None)
            
            return response
            
        except requests.exceptions.RequestException as e:
            # Handle and categorize the error
            error = handle_api_error(e, f"{self.service_name} API request", getattr(e, 'response', None))
            
            # Update health status
            self.health_checker._update_health_status(self.service_name, False, str(e))
            
            self.logger.error(f"Request failed: {method} {url} - {str(e)}")
            raise error
    
    def get(self, endpoint: str, **kwargs) -> requests.Response:
        """Make a GET request"""
        return self.request("GET", endpoint, **kwargs)
    
    def post(self, endpoint: str, **kwargs) -> requests.Response:
        """Make a POST request"""
        return self.request("POST", endpoint, **kwargs)
    
    def put(self, endpoint: str, **kwargs) -> requests.Response:
        """Make a PUT request"""
        return self.request("PUT", endpoint, **kwargs)
    
    def delete(self, endpoint: str, **kwargs) -> requests.Response:
        """Make a DELETE request"""
        return self.request("DELETE", endpoint, **kwargs)
    
    def health_check(self) -> bool:
        """Perform a health check on the service"""
        def check():
            try:
                # Try a simple request to check connectivity
                response = self.session.get(self.base_url, timeout=5)
                return response.status_code < 500
            except:
                return False
        
        return self.health_checker.check_health(self.service_name, check)
    
    def is_healthy(self) -> bool:
        """Check if the service is currently healthy"""
        return self.health_checker.is_healthy(self.service_name)
    
    def get_health_status(self) -> Dict:
        """Get detailed health status"""
        return self.health_checker.get_health_status(self.service_name)
