"""
Error handling and resilience utilities for Stellar Sync

This module provides comprehensive error handling, retry logic, circuit breaker patterns,
and rollback mechanisms for API operations.
"""

import time
import random
import logging
import traceback
from functools import wraps
from typing import Any, Callable, Dict, List, Optional, Type, Union
from enum import Enum
from dataclasses import dataclass
from datetime import datetime, timedelta
import requests
from requests.exceptions import RequestException, Timeout, ConnectionError, HTTPError


class ErrorCategory(Enum):
    """Categories of errors for different handling strategies"""
    NETWORK_ERROR = "network_error"
    RATE_LIMIT = "rate_limit"
    AUTHENTICATION = "authentication"
    VALIDATION = "validation"
    SERVER_ERROR = "server_error"
    CLIENT_ERROR = "client_error"
    TIMEOUT = "timeout"
    UNKNOWN = "unknown"


class RetryStrategy(Enum):
    """Different retry strategies"""
    EXPONENTIAL_BACKOFF = "exponential_backoff"
    LINEAR_BACKOFF = "linear_backoff"
    FIXED_DELAY = "fixed_delay"
    NO_RETRY = "no_retry"


@dataclass
class RetryConfig:
    """Configuration for retry behavior"""
    max_retries: int = 3
    base_delay: float = 1.0
    max_delay: float = 60.0
    strategy: RetryStrategy = RetryStrategy.EXPONENTIAL_BACKOFF
    jitter: bool = True
    backoff_multiplier: float = 2.0
    retryable_exceptions: tuple = (
        ConnectionError,
        Timeout,
        requests.exceptions.ConnectionError,
        requests.exceptions.Timeout,
        requests.exceptions.ChunkedEncodingError,
    )
    retryable_status_codes: tuple = (429, 500, 502, 503, 504)


@dataclass
class CircuitBreakerConfig:
    """Configuration for circuit breaker"""
    failure_threshold: int = 5
    recovery_timeout: int = 60
    expected_exception: Type[Exception] = Exception


class CircuitBreakerState(Enum):
    """Circuit breaker states"""
    CLOSED = "closed"
    OPEN = "open"
    HALF_OPEN = "half_open"


class CircuitBreaker:
    """Circuit breaker implementation to prevent cascading failures"""

    def __init__(self, config: CircuitBreakerConfig):
        self.config = config
        self.failure_count = 0
        self.last_failure_time = None
        self.state = CircuitBreakerState.CLOSED
        self.logger = logging.getLogger(__name__)

    def call(self, func: Callable, *args, **kwargs) -> Any:
        """Execute function with circuit breaker protection"""
        if self.state == CircuitBreakerState.OPEN:
            if self._should_attempt_reset():
                self.state = CircuitBreakerState.HALF_OPEN
                self.logger.info("Circuit breaker moving to HALF_OPEN state")
            else:
                raise CircuitBreakerOpenError("Circuit breaker is OPEN")

        try:
            result = func(*args, **kwargs)
            self._on_success()
            return result
        except self.config.expected_exception as e:
            self._on_failure()
            raise e

    def _should_attempt_reset(self) -> bool:
        """Check if enough time has passed to attempt reset"""
        if self.last_failure_time is None:
            return True
        return (datetime.now() - self.last_failure_time).seconds >= self.config.recovery_timeout

    def _on_success(self):
        """Handle successful call"""
        self.failure_count = 0
        if self.state == CircuitBreakerState.HALF_OPEN:
            self.state = CircuitBreakerState.CLOSED
            self.logger.info("Circuit breaker reset to CLOSED state")

    def _on_failure(self):
        """Handle failed call"""
        self.failure_count += 1
        self.last_failure_time = datetime.now()

        if self.failure_count >= self.config.failure_threshold:
            self.state = CircuitBreakerState.OPEN
            self.logger.warning(f"Circuit breaker opened after {self.failure_count} failures")


class CircuitBreakerOpenError(Exception):
    """Exception raised when circuit breaker is open"""
    pass


class StellarSyncError(Exception):
    """Base exception for Stellar Sync application"""

    def __init__(self, message: str, category: ErrorCategory = ErrorCategory.UNKNOWN,
                 details: Optional[Dict] = None, original_exception: Optional[Exception] = None):
        super().__init__(message)
        self.message = message
        self.category = category
        self.details = details or {}
        self.original_exception = original_exception
        self.timestamp = datetime.now()

    def to_dict(self) -> Dict:
        """Convert error to dictionary for logging/API responses"""
        return {
            "message": self.message,
            "category": self.category.value,
            "details": self.details,
            "timestamp": self.timestamp.isoformat(),
            "original_exception": str(self.original_exception) if self.original_exception else None
        }


class DiscogsAPIError(StellarSyncError):
    """Discogs API specific error"""
    pass


class ShopifyAPIError(StellarSyncError):
    """Shopify API specific error"""
    pass


class ValidationError(StellarSyncError):
    """Input validation error"""
    pass


class RollbackError(StellarSyncError):
    """Error during rollback operation"""
    pass


def categorize_error(exception: Exception, response: Optional[requests.Response] = None) -> ErrorCategory:
    """Categorize an error for appropriate handling"""

    if isinstance(exception, (ConnectionError, requests.exceptions.ConnectionError)):
        return ErrorCategory.NETWORK_ERROR

    if isinstance(exception, (Timeout, requests.exceptions.Timeout)):
        return ErrorCategory.TIMEOUT

    if response is not None:
        status_code = response.status_code

        if status_code == 401:
            return ErrorCategory.AUTHENTICATION
        elif status_code == 429:
            return ErrorCategory.RATE_LIMIT
        elif 400 <= status_code < 500:
            return ErrorCategory.CLIENT_ERROR
        elif 500 <= status_code < 600:
            return ErrorCategory.SERVER_ERROR

    if isinstance(exception, ValidationError):
        return ErrorCategory.VALIDATION

    return ErrorCategory.UNKNOWN


def calculate_delay(attempt: int, config: RetryConfig) -> float:
    """Calculate delay for retry attempt"""

    if config.strategy == RetryStrategy.EXPONENTIAL_BACKOFF:
        delay = config.base_delay * (config.backoff_multiplier ** attempt)
    elif config.strategy == RetryStrategy.LINEAR_BACKOFF:
        delay = config.base_delay * (attempt + 1)
    elif config.strategy == RetryStrategy.FIXED_DELAY:
        delay = config.base_delay
    else:
        return 0

    # Apply jitter to prevent thundering herd
    if config.jitter:
        delay += random.uniform(0, delay * 0.1)

    # Cap at max delay
    return min(delay, config.max_delay)


def retry_with_backoff(config: Optional[RetryConfig] = None):
    """
    Decorator to add retry logic with exponential backoff

    Args:
        config: RetryConfig object with retry parameters
    """
    if config is None:
        config = RetryConfig()

    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            logger = logging.getLogger(func.__module__)
            last_exception = None

            for attempt in range(config.max_retries + 1):
                try:
                    return func(*args, **kwargs)

                except Exception as e:
                    last_exception = e

                    # Check if this is the last attempt
                    if attempt == config.max_retries:
                        logger.error(f"Function {func.__name__} failed after {config.max_retries + 1} attempts: {str(e)}")
                        raise e

                    # Check if exception is retryable
                    should_retry = False

                    # Check exception type
                    if isinstance(e, config.retryable_exceptions):
                        should_retry = True

                    # Check HTTP status codes if it's an HTTP error
                    if isinstance(e, HTTPError) and hasattr(e, 'response') and e.response is not None:
                        if e.response.status_code in config.retryable_status_codes:
                            should_retry = True

                    if not should_retry:
                        logger.error(f"Function {func.__name__} failed with non-retryable error: {str(e)}")
                        raise e

                    # Calculate delay and wait
                    delay = calculate_delay(attempt, config)
                    logger.warning(f"Function {func.__name__} failed (attempt {attempt + 1}/{config.max_retries + 1}): {str(e)}. Retrying in {delay:.2f} seconds...")
                    time.sleep(delay)

            # This should never be reached, but just in case
            raise last_exception

        return wrapper
    return decorator


class RollbackAction:
    """Represents a rollback action that can be executed"""

    def __init__(self, action: Callable, args: tuple = (), kwargs: dict = None, description: str = ""):
        self.action = action
        self.args = args
        self.kwargs = kwargs or {}
        self.description = description

    def execute(self) -> bool:
        """Execute the rollback action"""
        try:
            self.action(*self.args, **self.kwargs)
            return True
        except Exception as e:
            logging.error(f"Rollback action failed: {self.description} - {str(e)}")
            return False


class TransactionManager:
    """Manages transaction-like behavior with rollback capabilities"""

    def __init__(self):
        self.rollback_actions: List[RollbackAction] = []
        self.completed_actions: List[str] = []
        self.logger = logging.getLogger(__name__)

    def add_rollback_action(self, action: Callable, args: tuple = (), kwargs: dict = None, description: str = ""):
        """Add a rollback action to be executed if transaction fails"""
        rollback_action = RollbackAction(action, args, kwargs, description)
        self.rollback_actions.append(rollback_action)
        self.logger.debug(f"Added rollback action: {description}")

    def mark_completed(self, action_description: str):
        """Mark an action as completed"""
        self.completed_actions.append(action_description)
        self.logger.debug(f"Marked action as completed: {action_description}")

    def rollback(self) -> bool:
        """Execute all rollback actions in reverse order"""
        if not self.rollback_actions:
            self.logger.info("No rollback actions to execute")
            return True

        self.logger.warning(f"Starting rollback of {len(self.rollback_actions)} actions")
        success_count = 0

        # Execute rollback actions in reverse order (LIFO)
        for action in reversed(self.rollback_actions):
            if action.execute():
                success_count += 1
                self.logger.info(f"Successfully rolled back: {action.description}")
            else:
                self.logger.error(f"Failed to rollback: {action.description}")

        rollback_success = success_count == len(self.rollback_actions)

        if rollback_success:
            self.logger.info("All rollback actions completed successfully")
        else:
            self.logger.error(f"Rollback partially failed: {success_count}/{len(self.rollback_actions)} actions succeeded")

        return rollback_success

    def clear(self):
        """Clear all rollback actions (call after successful transaction)"""
        self.rollback_actions.clear()
        self.completed_actions.clear()
        self.logger.debug("Transaction manager cleared")


def handle_api_error(exception: Exception, context: str = "", response: Optional[requests.Response] = None) -> StellarSyncError:
    """
    Convert generic exceptions to StellarSyncError with proper categorization

    Args:
        exception: The original exception
        context: Context information about where the error occurred
        response: HTTP response object if available

    Returns:
        StellarSyncError with proper categorization
    """
    category = categorize_error(exception, response)

    # Extract additional details from response
    details = {"context": context}
    if response is not None:
        details.update({
            "status_code": response.status_code,
            "response_text": response.text[:500] if response.text else None,  # Limit response text
            "url": response.url
        })

    # Create appropriate error type based on context
    if "discogs" in context.lower():
        return DiscogsAPIError(
            message=f"Discogs API error: {str(exception)}",
            category=category,
            details=details,
            original_exception=exception
        )
    elif "shopify" in context.lower():
        return ShopifyAPIError(
            message=f"Shopify API error: {str(exception)}",
            category=category,
            details=details,
            original_exception=exception
        )
    else:
        return StellarSyncError(
            message=f"API error in {context}: {str(exception)}",
            category=category,
            details=details,
            original_exception=exception
        )


def log_error_with_context(error: StellarSyncError, logger: Optional[logging.Logger] = None):
    """
    Log error with full context information

    Args:
        error: StellarSyncError to log
        logger: Logger to use (defaults to module logger)
    """
    if logger is None:
        logger = logging.getLogger(__name__)

    error_dict = error.to_dict()

    # Log with appropriate level based on category
    if error.category in [ErrorCategory.NETWORK_ERROR, ErrorCategory.TIMEOUT]:
        log_level = logging.WARNING
    elif error.category in [ErrorCategory.RATE_LIMIT]:
        log_level = logging.INFO
    else:
        log_level = logging.ERROR

    logger.log(log_level, f"Error occurred: {error.message}")
    logger.debug(f"Error details: {error_dict}")

    # Log stack trace for debugging
    if error.original_exception:
        logger.debug(f"Original exception: {traceback.format_exception(type(error.original_exception), error.original_exception, error.original_exception.__traceback__)}")


def create_error_response(error: StellarSyncError, include_details: bool = False) -> Dict:
    """
    Create a standardized error response for API endpoints

    Args:
        error: StellarSyncError to convert
        include_details: Whether to include detailed error information

    Returns:
        Dictionary suitable for JSON response
    """
    response = {
        "success": False,
        "error": {
            "message": error.message,
            "category": error.category.value,
            "timestamp": error.timestamp.isoformat()
        }
    }

    if include_details and error.details:
        response["error"]["details"] = error.details

    return response


# Global circuit breakers for different services
_discogs_circuit_breaker = CircuitBreaker(CircuitBreakerConfig(
    failure_threshold=3,
    recovery_timeout=30,
    expected_exception=DiscogsAPIError
))

_shopify_circuit_breaker = CircuitBreaker(CircuitBreakerConfig(
    failure_threshold=3,
    recovery_timeout=30,
    expected_exception=ShopifyAPIError
))


def get_discogs_circuit_breaker() -> CircuitBreaker:
    """Get the global Discogs circuit breaker"""
    return _discogs_circuit_breaker


def get_shopify_circuit_breaker() -> CircuitBreaker:
    """Get the global Shopify circuit breaker"""
    return _shopify_circuit_breaker
