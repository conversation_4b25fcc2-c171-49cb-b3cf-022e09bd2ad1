@echo off

REM Check if the container is already running
docker ps -f name=stellar-records --format "{{.Names}}" | findstr "stellar-records"

REM If the container is not running, start it
if errorlevel 1 (
    docker run -d -p 5000:5000 --name stellar-records stellar-records

    REM Wait for the container to be ready
    :waitloop
    docker exec stellar-records flask --version 2>nul 1>nul
    if errorlevel 1 (
        echo Waiting for the container to start...
        timeout /t 1 >nul
        goto waitloop
    )
) else (
    echo Container is already running.
)

start /B "" "http://localhost:5000"
