# For use with Rest Client vscode extension: https://marketplace.visualstudio.com/items?itemName=humao.rest-client

##########
### Test /create_discog_and_shopify_listings
##########

### Invalid request should fail
POST http://localhost:5000/create_discog_and_shopify_listings
Content-Type: application/json

{"blah":"blah"}



### Invalid request with a few missing fields
POST http://localhost:5000/create_discog_and_shopify_listings
Content-Type: application/json

{
    "create_on_discogs": true,
    "selected_collections": ["1234","345345"],
    "discogs_release_id": "12345",
    "media_condition": "Excellent",
    "sleeve_condition": "Good",
    "price_str": "25.99",
    "comments": "Some comments",
    "bin_location": "A1"
}

### Invalid request because collections is not a list
POST http://localhost:5000/create_discog_and_shopify_listings
Content-Type: application/json

{
    "create_on_discogs": true,
    "selected_collections": "234",
    "discogs_release_id": "12345",
    "media_condition": "Excellent",
    "sleeve_condition": "Good",
    "price_str": "25.99",
    "comments": "Some comments",
    "bin_location": "A1",
    "quantity": 1,
    "price": 20.00
}

### Invalid request because collections is does not contain ints
POST http://localhost:5000/create_discog_and_shopify_listings
Content-Type: application/json

{
    "create_on_discogs": true,
    "selected_collections": ["234", "2345"],
    "discogs_release_id": "12345",
    "media_condition": "Excellent",
    "sleeve_condition": "Good",
    "price_str": "25.99",
    "comments": "Some comments",
    "bin_location": "A1",
    "quantity": 1,
    "price": 20.00
}

### Valid request but fake collections
POST http://localhost:5000/create_discog_and_shopify_listings
Content-Type: application/json

{
    "create_on_discogs": true,
    "selected_collections": [234, 2345],
    "discogs_release_id": "12345",
    "media_condition": "Mint (M)",
    "sleeve_condition": "Good Plus (G+)",
    "price_str": "25.99",
    "comments": "Some comments",
    "bin_location": "A1",
    "quantity": 1,
    "price": 20.00
}




##########
### Test /shopify/custom_collections
##########

GET http://localhost:5000/shopify/custom_collections


##########
### Test /discogs/conditions
##########

GET http://localhost:5000/discogs/conditions