import requests
import re
import logging
from third_parties.discogs import Condition, DiscogsListing, DiscogsAPIClient
from third_parties.shopify import ShopifyAPIClient
import os
from utils.logging_utils import log_new_listing, log_error
from utils.error_handling import (
    StellarSyncError,
    Discogs<PERSON>IError,
    Shopify<PERSON>IError,
    TransactionManager,
    retry_with_backoff,
    RetryConfig,
    handle_api_error,
    log_error_with_context
)

# # Whether to run in debug mode
DEBUG = False

# Set up logging
logger = logging.getLogger(__name__)

# Discogs API Endpoint for creating a new listing
DISCOGS_API_URL = os.getenv("DISCOGS_API_URL")
DISCOGS_TOKEN = os.getenv("DISCOGS_TOKEN")

# Shopify API information
SHOPIFY_TOKEN = os.getenv("SHOPIFY_TOKEN")
SHOPIFY_SHOP_NAME = os.getenv("SHOPIFY_SHOP_NAME")

# global clients
discogs_client = DiscogsAPIClient(token=DISCOGS_TOKEN, verify_ssl=False)
shopify_client = ShopifyAPIClient(
    shop_name=SHOPIFY_SHOP_NAME, access_token=SHOPIFY_TOKEN, verify_ssl=False
)


def ask_for_discogs_condition(condition_for):
    print(
        f"Please select a Discogs condition for the {condition_for} from the following list:"
    )

    for i, condition in enumerate(Condition, 1):
        print(f"{i}. {condition.value}")

    while True:
        try:
            selection = int(input("Enter the number corresponding to your choice: "))
            if 1 <= selection <= len(Condition):
                return list(Condition)[selection - 1].value
            else:
                print("Invalid choice. Please select a number from the list.")
        except ValueError:
            print("Please enter a valid number from the list.")


def ask_for_shopify_custom_collections():
    collections = shopify_client.get_custom_collections()

    print("Please select custom collections for the product:")
    for idx, collection in enumerate(collections, 1):
        print(f"{idx}. {collection['title']}")

    selected_collections = []
    while True:
        try:
            selections = input(
                "Enter the numbers corresponding to your choices (comma separated): "
            ).split(",")
            for selection in selections:
                sel = int(selection.strip())
                if 1 <= sel <= len(collections):
                    selected_collections.append(collections[sel - 1]["id"])
                else:
                    print("Invalid choice. Please select numbers from the list.")
                    continue
            break
        except ValueError:
            print("Please enter valid numbers from the list.")
    return selected_collections


def ask_for_price():
    while True:
        try:
            price_str = input("Please enter the price of the item: $")
            price_float = float(price_str)
            return price_float
        except ValueError:
            print("Invalid input. Please enter a valid numerical price.")


def ask_for_release_id():
    while True:
        try:
            discogs_release_id = input("Enter the release id from Discogs: ")
            listing = discogs_client.get_release(discogs_id=discogs_release_id)
            print(f"Release found: {listing['artists_sort']} - {listing['title']}")
            try:
                choice = input("Is this the correct release? (yes/no):").strip().lower()
                if choice == "yes" or choice == "y":
                    return discogs_release_id
                else:
                    raise ValueError
            except ValueError:
                print("Invalid choice.")
        except requests.HTTPError as e:
            print(
                f"Error: '{discogs_release_id}' is not a valid discogs release id. {e.response.text}"
            )


def ask_for_discog_creation() -> bool:
    create_on_discogs = (
        input("Do you want to create the listing on Discogs as well? (yes/no): ")
        .strip()
        .lower()
    )

    if create_on_discogs == "yes" or create_on_discogs == "y":
        return True
    else:
        return False


def generate_shopify_product_data(price_float, quantity, discogs_data, user_description=None, product_condition=None, barcode=None, collection_names=None, product_status="active", media_type=None, channels=None, speed=None, release_year=None):
    # Create base title
    if DEBUG:
        title = f"TEST ENTRY NOT FOR SALE {discogs_data['artists'][0]['name']} - {discogs_data['title']}"
    else:
        title = f"{discogs_data['artists'][0]['name']} - {discogs_data['title']}"

    # Append product condition to title if provided
    if product_condition:
        if product_condition == "New":
            title += " - New"
        elif product_condition == "Used":
            title += " - Used"

    # Append media type to title if provided
    if media_type:
        title += f" - {media_type}"

    # Append channels to title if provided
    if channels:
        title += f" - {channels}"

    # Append speed to title if provided and not N/A
    if speed and speed != "N/A":
        title += f" - {speed}"

    # Append year to title if provided
    if release_year and release_year.strip():
        title += f" - {release_year}"

    tracklist = discogs_data.get("tracklist", [])
    releaseDate = discogs_data.get("released_formatted", "")

    # Get full-sized image instead of thumbnail if available
    images = discogs_data.get("images", [])
    if images and len(images) > 0:
        # Use the primary image (usually the first one)
        primary_images = [img for img in images if img.get("type") == "primary"]
        if primary_images:
            thumb_url = primary_images[0].get("uri", "")
        else:
            # Fall back to the first image if no primary image is found
            thumb_url = images[0].get("uri", "")
    else:
        # Fall back to thumbnail if no images are available
        thumb_url = discogs_data.get("thumb", "")

    # Use user-provided description if available, otherwise use empty string
    description = user_description if user_description else ""

    # Build the HTML body with user description
    body_html = f"<p>{description}</p>"

    # Add release date if available
    if releaseDate:
        body_html += f"<br><p>Release Date: {releaseDate}</p>"

    # Format the tracklist as a styled div that matches the rest of the description
    body_html += f"<br><p>Tracklist:</p><div style='white-space: pre-wrap; background: transparent;'>"

    # Add each track as a separate line
    for track in tracklist:
        body_html += f"{track['position']} {track['title']}<br>"

    body_html += "</div>"

    # Create product data dictionary
    product_data = {
        "product": {
            "title": title,
            "body_html": body_html,
            "product_type": "Records & LPs",
            "variants": [
                {
                    "price": str(price_float),
                    "inventory_management": "shopify",
                    "inventory_policy": "deny",
                    "inventory_quantity": quantity,
                    "barcode": barcode if barcode else None,
                }
            ],
            "status": product_status.lower(),  # Set product status (active or draft)
        }
    }

    # Set published flag based on product status
    if product_status.lower() == "active":
        product_data["product"]["published"] = True
    else:
        product_data["product"]["published"] = False

    # Set published_scope to global (all channels)
    product_data["product"]["published_scope"] = "global"
    print("Setting published_scope to 'global' for all sales channels")

    # Add collection names as tags if provided
    if collection_names and len(collection_names) > 0:
        product_data["product"]["tags"] = ", ".join(collection_names)
        print(f"Adding tags to product: {product_data['product']['tags']}")

    return thumb_url, product_data


def _delete_shopify_product(product_id: int) -> bool:
    """
    Helper function to delete a Shopify product (for rollback)

    Args:
        product_id: Shopify product ID to delete

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Note: Shopify doesn't have a direct delete product endpoint in the current API client
        # For now, we'll set the product to draft status and add a tag to mark it for deletion
        logger.info(f"Marking Shopify product {product_id} for deletion (rollback)")

        # This is a placeholder - in a real implementation, you might want to:
        # 1. Set product status to draft
        # 2. Add a "TO_DELETE" tag
        # 3. Or implement actual product deletion if the API supports it

        # For now, just log the action
        logger.warning(f"Shopify product {product_id} marked for manual deletion")
        return True

    except Exception as e:
        logger.error(f"Failed to delete Shopify product {product_id}: {str(e)}")
        return False


def _delete_discogs_listing(listing_id: str) -> bool:
    """
    Helper function to delete a Discogs listing (for rollback)

    Args:
        listing_id: Discogs listing ID to delete

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        logger.info(f"Deleting Discogs listing {listing_id} (rollback)")
        discogs_client.delete_listing(listing_id)
        logger.info(f"Successfully deleted Discogs listing {listing_id}")
        return True

    except Exception as e:
        logger.error(f"Failed to delete Discogs listing {listing_id}: {str(e)}")
        return False


@retry_with_backoff(RetryConfig(max_retries=2, base_delay=2.0))
def create_discog_and_shopify_listings(
    new_listing, create_on_discogs, selected_collections, quantity, description=None, product_condition=None, barcode=None,
    product_status="active", media_type=None, channels=None, speed=None, release_year=None
):
    """
    Create listings on Discogs and/or Shopify with transaction management and rollback capabilities

    Returns:
        tuple: (discogs_listing_ids, shopify_url)

    Raises:
        StellarSyncError: On critical failures
    """
    transaction_manager = TransactionManager()
    discogs_listing_ids = []
    product_id = None
    product = None

    try:
        logger.info(f"Starting listing creation for release {new_listing.release_id}")

        # Step 1: Get release data from Discogs
        logger.debug("Fetching release data from Discogs")
        discogs_data = discogs_client.get_release(discogs_id=new_listing.release_id)
        transaction_manager.mark_completed("Fetched Discogs release data")

        # Step 2: Get collection names from collection IDs
        logger.debug("Fetching collection names from Shopify")
        collection_names = shopify_client.get_collection_names_from_ids(selected_collections)
        logger.info(f"Selected collections: {collection_names}")

        # Step 3: Generate product data
        logger.debug("Generating Shopify product data")
        thumb_url, product_data = generate_shopify_product_data(
            price_float=new_listing.price,
            quantity=quantity,
            discogs_data=discogs_data,
            user_description=description,
            product_condition=product_condition,
            barcode=barcode,
            collection_names=collection_names,
            product_status=product_status,
            media_type=media_type,
            channels=channels,
            speed=speed,
            release_year=release_year
        )

        # Step 4: Create Shopify product
        logger.info("Creating Shopify product")
        product = shopify_client.create_product(product_data=product_data)
        product_id = product.get("id")

        if product_id:
            logger.info(f"Successfully created Shopify listing {product_id}")

            # Add rollback action for Shopify product deletion
            transaction_manager.add_rollback_action(
                action=_delete_shopify_product,
                args=(product_id,),
                description=f"Delete Shopify product {product_id}"
            )
            transaction_manager.mark_completed("Created Shopify product")

            # Step 5: Add product to custom collections
            if selected_collections:
                logger.debug("Adding product to custom collections")
                shopify_client.add_product_to_custom_collections(
                    product_id=product_id, collection_ids=selected_collections
                )
                logger.info(f"Successfully added Shopify product {product_id} to custom collections")
                transaction_manager.mark_completed("Added product to collections")

            # Step 6: Update product image
            if thumb_url:
                logger.debug("Updating product image")
                shopify_client.update_product_image(product_id, thumb_url)
                logger.info(f"Successfully updated Shopify product image for ID: {product_id}")
                transaction_manager.mark_completed("Updated product image")
            else:
                logger.warning("No image URL available to update the product image")

            # Step 7: Store Discogs release ID as a metafield
            logger.debug("Creating Discogs release ID metafield")
            shopify_client.create_metafield(
                product_id=product_id,
                namespace="discogs",
                key="release_id",
                value=str(new_listing.release_id),
                value_type="string"
            )
            logger.info(f"Successfully stored Discogs release ID {new_listing.release_id} as metafield")
            transaction_manager.mark_completed("Created Discogs metafield")

            logger.info(f"Product {product_id} is published globally to all sales channels")
        else:
            error_msg = "Failed to create Shopify product"
            logger.error(error_msg)
            raise ShopifyAPIError(error_msg)

        # Step 8: Create Discogs listings if requested
        if create_on_discogs:
            logger.info(f"Creating {quantity} Discogs listings")
            discogs_listings = []

            for i in range(quantity):
                try:
                    logger.debug(f"Creating Discogs listing {i+1}/{quantity}")
                    discogs_listing = discogs_client.create_listing(new_listing)
                    discogs_listings.append(discogs_listing)

                    # Add rollback action for each Discogs listing
                    listing_id = discogs_listing.get("listing_id")
                    if listing_id:
                        transaction_manager.add_rollback_action(
                            action=_delete_discogs_listing,
                            args=(listing_id,),
                            description=f"Delete Discogs listing {listing_id}"
                        )

                except Exception as e:
                    logger.error(f"Failed to create Discogs listing {i+1}: {str(e)}")
                    # If we fail to create any Discogs listing, rollback everything
                    transaction_manager.rollback()
                    raise DiscogsAPIError(f"Failed to create Discogs listing {i+1}: {str(e)}")

            discogs_listing_ids = [listing["listing_id"] for listing in discogs_listings]
            logger.info(f"Successfully created {len(discogs_listing_ids)} Discogs listings")
            transaction_manager.mark_completed("Created Discogs listings")

        # Step 9: Log the new listing
        artist = discogs_data['artists'][0]['name'] if discogs_data.get('artists') else "Unknown Artist"
        title = discogs_data.get('title', "Unknown Title")

        try:
            # Determine which platforms were used
            platform = "both" if create_on_discogs and product_id else "shopify" if product_id else "discogs"

            # Log the listing creation
            log_new_listing(
                release_id=new_listing.release_id,
                artist=artist,
                title=title,
                price=new_listing.price,
                condition=product_condition or new_listing.condition.value,
                platform=platform,
                listing_id=','.join(map(str, discogs_listing_ids)) if discogs_listing_ids else None,
                product_id=product_id
            )
            transaction_manager.mark_completed("Logged listing creation")

        except Exception as e:
            # Log any errors that occur during logging (but don't fail the transaction)
            logger.warning(f"Failed to log listing creation: {str(e)}")
            log_error("Logging Error", str(e), release_id=new_listing.release_id)

        # Success! Clear rollback actions
        transaction_manager.clear()

        # Return listing IDs and Shopify URL
        shopify_url = f"https://stellarrecordsfl.com/products/{product.get('handle')}" if product.get('handle') else None
        logger.info(f"Successfully completed listing creation for release {new_listing.release_id}")

        return discogs_listing_ids, shopify_url

    except Exception as e:
        logger.error(f"Error during listing creation: {str(e)}")

        # Attempt rollback
        logger.warning("Attempting to rollback partial changes")
        rollback_success = transaction_manager.rollback()

        if not rollback_success:
            logger.error("Rollback failed - manual cleanup may be required")
            # Log the rollback failure for manual intervention
            log_error("Rollback Failed",
                     f"Failed to rollback changes for release {new_listing.release_id}. "
                     f"Manual cleanup required. Product ID: {product_id}, "
                     f"Discogs listing IDs: {discogs_listing_ids}",
                     release_id=new_listing.release_id)

        # Re-raise the original error
        if isinstance(e, StellarSyncError):
            raise e
        else:
            raise StellarSyncError(f"Failed to create listings: {str(e)}", original_exception=e)


def gather_data_from_console():
    # Ask the user if they want to create the listing on Discogs
    create_on_discogs = ask_for_discog_creation()

    # Discogs Listing
    discogs_release_id = ask_for_release_id()
    media_condition = ask_for_discogs_condition("media")
    sleeve_condition = ask_for_discogs_condition("sleeve")

    # Get and Validate price
    price_float = ask_for_price()
    quantity = ask_for_quantity()

    comments = input("Enter comments to display on listing: ")
    bin_location = input("Enter the location in the store the item can be found: ")

    new_listing = DiscogsListing(
        release_id=discogs_release_id,
        condition=media_condition,
        sleeve_condition=sleeve_condition,
        price=price_float,
        comments=comments,
        status="For Sale",
        location=bin_location,
    )

    selected_collections = ask_for_shopify_custom_collections()

    return new_listing, create_on_discogs, selected_collections, quantity


def ask_for_quantity():
    while True:
        try:
            quantity = input("Enter quantity: ")
            quantity = int(quantity)
            if quantity > 0 and quantity < 100:
                return quantity
            else:
                raise ValueError
        except ValueError:
            print("Invalid input. Please enter a valid numerical quantity.")


if __name__ == "__main__":
    # Running in console mode
    (
        new_listing,
        create_on_discogs,
        selected_collections,
        quantity,
    ) = gather_data_from_console()

    # Ask for media type
    media_type = input("Enter media type (e.g., LP, CD, Cassette): ")

    # Ask for channels
    channels_input = input("Enter channels (Stereo/Mono) [Default: Stereo]: ")
    channels = channels_input if channels_input.strip() else "Stereo"

    # Ask for speed
    speed_input = input("Enter speed (33 ⅓ RPM/45 RPM/N/A) [Default: N/A]: ")
    speed = speed_input if speed_input.strip() else "N/A"

    # Ask for release year
    while True:
        release_year_input = input("Enter release year (4-digit year or leave blank): ")
        if not release_year_input.strip():
            release_year = ""
            break
        elif re.match(r'^(19|20)\d{2}$', release_year_input):
            release_year = release_year_input
            break
        else:
            print("Invalid year format. Please enter a 4-digit year (1900-2099) or leave blank.")

    discog_listing_ids, shopify_url = create_discog_and_shopify_listings(
        new_listing=new_listing,
        create_on_discogs=create_on_discogs,
        selected_collections=selected_collections,
        quantity=quantity,
        media_type=media_type,
        channels=channels,
        speed=speed,
        release_year=release_year
    )


    print("Successfully created listings:")
    if create_on_discogs:
        print(
            f"Discogs Listings: {', '.join(map(str, discog_listing_ids))} - https://www.discogs.com/sell/manage"
        )
    print(
        f"Shopify Listing: {shopify_url}"
    )
