import requests
from third_parties.discogs import Condition, DiscogsListing, DiscogsAPIClient
from third_parties.shopify import ShopifyAPIClient
import os
from utils.logging_utils import log_new_listing, log_error

# # Whether to run in debug mode
DEBUG = False

# Discogs API Endpoint for creating a new listing
DISCOGS_API_URL = os.getenv("DISCOGS_API_URL")
DISCOGS_TOKEN = os.getenv("DISCOGS_TOKEN")

# Shopify API information
SHOPIFY_TOKEN = os.getenv("SHOPIFY_TOKEN")
SHOPIFY_SHOP_NAME = os.getenv("SHOPIFY_SHOP_NAME")

# global clients
discogs_client = DiscogsAPIClient(token=DISCOGS_TOKEN, verify_ssl=False)
shopify_client = ShopifyAPIClient(
    shop_name=SHOPIFY_SHOP_NAME, access_token=SHOPIFY_TOKEN, verify_ssl=False
)


def ask_for_discogs_condition(condition_for):
    print(
        f"Please select a Discogs condition for the {condition_for} from the following list:"
    )

    for i, condition in enumerate(Condition, 1):
        print(f"{i}. {condition.value}")

    while True:
        try:
            selection = int(input("Enter the number corresponding to your choice: "))
            if 1 <= selection <= len(Condition):
                return list(Condition)[selection - 1].value
            else:
                print("Invalid choice. Please select a number from the list.")
        except ValueError:
            print("Please enter a valid number from the list.")


def ask_for_shopify_custom_collections():
    collections = shopify_client.get_custom_collections()

    print("Please select custom collections for the product:")
    for idx, collection in enumerate(collections, 1):
        print(f"{idx}. {collection['title']}")

    selected_collections = []
    while True:
        try:
            selections = input(
                "Enter the numbers corresponding to your choices (comma separated): "
            ).split(",")
            for selection in selections:
                sel = int(selection.strip())
                if 1 <= sel <= len(collections):
                    selected_collections.append(collections[sel - 1]["id"])
                else:
                    print("Invalid choice. Please select numbers from the list.")
                    continue
            break
        except ValueError:
            print("Please enter valid numbers from the list.")
    return selected_collections


def ask_for_price():
    while True:
        try:
            price_str = input("Please enter the price of the item: $")
            price_float = float(price_str)
            return price_float
        except ValueError:
            print("Invalid input. Please enter a valid numerical price.")


def ask_for_release_id():
    while True:
        try:
            discogs_release_id = input("Enter the release id from Discogs: ")
            listing = discogs_client.get_release(discogs_id=discogs_release_id)
            print(f"Release found: {listing['artists_sort']} - {listing['title']}")
            try:
                choice = input("Is this the correct release? (yes/no):").strip().lower()
                if choice == "yes" or choice == "y":
                    return discogs_release_id
                else:
                    raise ValueError
            except ValueError:
                print("Invalid choice.")
        except requests.HTTPError as e:
            print(
                f"Error: '{discogs_release_id}' is not a valid discogs release id. {e.response.text}"
            )


def ask_for_discog_creation() -> bool:
    create_on_discogs = (
        input("Do you want to create the listing on Discogs as well? (yes/no): ")
        .strip()
        .lower()
    )

    if create_on_discogs == "yes" or create_on_discogs == "y":
        return True
    else:
        return False


def generate_shopify_product_data(price_float, quantity, discogs_data, user_description=None, product_condition=None, barcode=None, collection_names=None, product_status="active"):
    # Create base title
    if DEBUG:
        title = f"TEST ENTRY NOT FOR SALE {discogs_data['artists'][0]['name']} - {discogs_data['title']}"
    else:
        title = f"{discogs_data['artists'][0]['name']} - {discogs_data['title']}"

    # Append product condition to title if provided
    if product_condition:
        if product_condition == "New":
            title += " - New"
        elif product_condition == "Used":
            title += " - Used"

    tracklist = discogs_data.get("tracklist", [])
    releaseDate = discogs_data.get("released_formatted", "")

    # Get full-sized image instead of thumbnail if available
    images = discogs_data.get("images", [])
    if images and len(images) > 0:
        # Use the primary image (usually the first one)
        primary_images = [img for img in images if img.get("type") == "primary"]
        if primary_images:
            thumb_url = primary_images[0].get("uri", "")
        else:
            # Fall back to the first image if no primary image is found
            thumb_url = images[0].get("uri", "")
    else:
        # Fall back to thumbnail if no images are available
        thumb_url = discogs_data.get("thumb", "")

    # Use user-provided description if available, otherwise use empty string
    description = user_description if user_description else ""

    # Build the HTML body with user description
    body_html = f"<p>{description}</p>"

    # Add release date if available
    if releaseDate:
        body_html += f"<p>Release Date: {releaseDate}</p>"

    # Format the tracklist as a styled div that matches the rest of the description
    body_html += f"<p>Tracklist:</p><div style='white-space: pre-wrap; background: transparent;'>"

    # Add each track as a separate line
    for track in tracklist:
        body_html += f"{track['position']} {track['title']}<br>"

    body_html += "</div>"

    # Create product data dictionary
    product_data = {
        "product": {
            "title": title,
            "body_html": body_html,
            "product_type": "Records & LPs",
            "variants": [
                {
                    "price": str(price_float),
                    "inventory_management": "shopify",
                    "inventory_policy": "deny",
                    "inventory_quantity": quantity,
                    "barcode": barcode if barcode else None,
                }
            ],
            "status": product_status.lower(),  # Set product status (active or draft)
        }
    }

    # Set published flag based on product status
    if product_status.lower() == "active":
        product_data["product"]["published"] = True
    else:
        product_data["product"]["published"] = False

    # Set published_scope to global (all channels)
    product_data["product"]["published_scope"] = "global"
    print("Setting published_scope to 'global' for all sales channels")

    # Add collection names as tags if provided
    if collection_names and len(collection_names) > 0:
        product_data["product"]["tags"] = ", ".join(collection_names)
        print(f"Adding tags to product: {product_data['product']['tags']}")

    return thumb_url, product_data


def create_discog_and_shopify_listings(
    new_listing, create_on_discogs, selected_collections, quantity, description=None, product_condition=None, barcode=None,
    product_status="active"
):
    discogs_listing_ids = []

    # Shopify Listing - Get release data first
    discogs_data = discogs_client.get_release(discogs_id=new_listing.release_id)

    # Get collection names from collection IDs
    collection_names = shopify_client.get_collection_names_from_ids(selected_collections)
    print(f"Selected collections: {collection_names}")

    # Generate product data
    thumb_url, product_data = generate_shopify_product_data(
        price_float=new_listing.price,
        quantity=quantity,
        discogs_data=discogs_data,
        user_description=description,
        product_condition=product_condition,
        barcode=barcode,
        collection_names=collection_names,
        product_status=product_status
    )

    # Create Shopify product
    product = shopify_client.create_product(product_data=product_data)
    product_id = product.get("id")

    if product_id:
        print(f"Successfully created Shopify listing {product_id}.")

        # Add product to custom collections
        shopify_client.add_product_to_custom_collections(
            product_id=product_id, collection_ids=selected_collections
        )
        print(f"Successfully added Shopify product {product_id} to custom collections.")

        # Update product image
        if thumb_url:
            shopify_client.update_product_image(product_id, thumb_url)
            print(f"Successfully updated Shopify product image for ID: {product_id}")
        else:
            print("No image URL available to update the product image.")

        # Store Discogs release ID as a metafield
        shopify_client.create_metafield(
            product_id=product_id,
            namespace="discogs",
            key="release_id",
            value=str(new_listing.release_id),
            value_type="string"
        )
        print(f"Successfully stored Discogs release ID {new_listing.release_id} as a metafield for Shopify product {product_id}")

        # Product is published globally to all sales channels by default
        print(f"Product {product_id} is published globally to all sales channels")
    else:
        print("Failed to create Shopify product.")

    # Create Discogs listings if requested
    if create_on_discogs:
        discogs_listings = []
        for _ in range(quantity):
            # Call the create_listing function and append the result to the list
            discogs_listing = discogs_client.create_listing(new_listing)
            discogs_listings.append(discogs_listing)

        discogs_listing_ids = [listing["listing_id"] for listing in discogs_listings]

    # Extract artist and title for logging
    artist = discogs_data['artists'][0]['name'] if discogs_data.get('artists') else "Unknown Artist"
    title = discogs_data.get('title', "Unknown Title")

    # Log the new listing
    try:
        # Determine which platforms were used
        platform = "both" if create_on_discogs and product_id else "shopify" if product_id else "discogs"

        # Log the listing creation
        log_new_listing(
            release_id=new_listing.release_id,
            artist=artist,
            title=title,
            price=new_listing.price,
            condition=product_condition or new_listing.condition.value,
            platform=platform,
            listing_id=','.join(map(str, discogs_listing_ids)) if discogs_listing_ids else None,
            product_id=product_id
        )
    except Exception as e:
        # Log any errors that occur during logging
        log_error("Logging Error", str(e), release_id=new_listing.release_id)

    # Return listing IDs and Shopify URL
    shopify_url = f"https://stellarrecordsfl.com/products/{product.get('handle')}" if product.get('handle') else None
    return discogs_listing_ids, shopify_url


def gather_data_from_console():
    # Ask the user if they want to create the listing on Discogs
    create_on_discogs = ask_for_discog_creation()

    # Discogs Listing
    discogs_release_id = ask_for_release_id()
    media_condition = ask_for_discogs_condition("media")
    sleeve_condition = ask_for_discogs_condition("sleeve")

    # Get and Validate price
    price_float = ask_for_price()
    quantity = ask_for_quantity()

    comments = input("Enter comments to display on listing: ")
    bin_location = input("Enter the location in the store the item can be found: ")

    new_listing = DiscogsListing(
        release_id=discogs_release_id,
        condition=media_condition,
        sleeve_condition=sleeve_condition,
        price=price_float,
        comments=comments,
        status="For Sale",
        location=bin_location,
    )

    selected_collections = ask_for_shopify_custom_collections()

    return new_listing, create_on_discogs, selected_collections, quantity


def ask_for_quantity():
    while True:
        try:
            quantity = input("Enter quantity: ")
            quantity = int(quantity)
            if quantity > 0 and quantity < 100:
                return quantity
            else:
                raise ValueError
        except ValueError:
            print("Invalid input. Please enter a valid numerical quantity.")


if __name__ == "__main__":
    # Running in console mode
    (
        new_listing,
        create_on_discogs,
        selected_collections,
        quantity,
    ) = gather_data_from_console()

    discog_listing_ids, shopify_url = create_discog_and_shopify_listings(
        new_listing=new_listing,
        create_on_discogs=create_on_discogs,
        selected_collections=selected_collections,
        quantity=quantity
    )


    print("Successfully created listings:")
    if create_on_discogs:
        print(
            f"Discogs Listings: {', '.join(map(str, discog_listing_ids))} - https://www.discogs.com/sell/manage"
        )
    print(
        f"Shopify Listing: {shopify_url}"
    )
