import requests
import warnings


class ShopifyAPIClient:
    def __init__(self, shop_name, access_token, verify_ssl=False):
        self.shop_name = shop_name
        self.access_token = access_token
        self.api_version = "2022-04"  # Shopify API version
        self.verify_ssl = verify_ssl

        if not verify_ssl:
            # Suppress only the InsecureRequestWarning
            warnings.filterwarnings('ignore', 'Unverified HTTPS request is being made.*')

    def create_product(self, product_data):
        url = f"https://{self.shop_name}.myshopify.com/admin/api/{self.api_version}/products.json"
        headers = {
            "X-Shopify-Access-Token": self.access_token,
            "Content-Type": "application/json",
        }
        response = requests.post(url, headers=headers, json=product_data, verify=self.verify_ssl)
        product = response.json().get("product", {})

        return product

    def add_product_to_custom_collections(self, product_id, collection_ids):
        headers = {
            "X-Shopify-Access-Token": self.access_token,
            "Content-Type": "application/json",
        }
        print(f"Adding product {product_id} to collections: {collection_ids}")
        for collection_id in collection_ids:
            url = f"https://{self.shop_name}.myshopify.com/admin/api/{self.api_version}/collects.json"
            collect_data = {
                "collect": {"product_id": product_id, "collection_id": collection_id}
            }
            response = requests.post(url, headers=headers, json=collect_data, verify=self.verify_ssl)
            if response.status_code not in (200, 201):
                print(f"Failed to add product to collection {collection_id}: {response.text}")

    def update_product_image(self, product_id, thumb_url):
        url = f"https://{self.shop_name}.myshopify.com/admin/api/{self.api_version}/products/{product_id}/images.json"
        headers = {
            "X-Shopify-Access-Token": self.access_token,
            "Content-Type": "application/json",
        }
        image_data = {"image": {"src": thumb_url}}
        response = requests.post(url, headers=headers, json=image_data, verify=self.verify_ssl)
        return response.json()

    def get_inventory_item_id(self, variant_id):
        """Get the inventory item ID for a variant"""
        url = f"https://{self.shop_name}.myshopify.com/admin/api/{self.api_version}/variants/{variant_id}.json"
        headers = {
            "X-Shopify-Access-Token": self.access_token,
            "Content-Type": "application/json",
        }
        response = requests.get(url, headers=headers, verify=self.verify_ssl)
        data = response.json().get("variant", {})
        return data.get("inventory_item_id")

    def get_inventory_levels(self, inventory_item_id):
        """Get inventory levels for an inventory item"""
        url = f"https://{self.shop_name}.myshopify.com/admin/api/{self.api_version}/inventory_levels.json?inventory_item_ids={inventory_item_id}"
        headers = {
            "X-Shopify-Access-Token": self.access_token,
            "Content-Type": "application/json",
        }
        response = requests.get(url, headers=headers, verify=self.verify_ssl)
        return response.json().get("inventory_levels", [])

    def update_inventory_level(self, variant_id, quantity):
        """Update inventory level for a variant"""
        # First, get the inventory item ID
        inventory_item_id = self.get_inventory_item_id(variant_id)
        if not inventory_item_id:
            return False

        # Then, get the inventory levels to find the location ID
        inventory_levels = self.get_inventory_levels(inventory_item_id)
        if not inventory_levels:
            return False

        # Use the first location
        location_id = inventory_levels[0].get("location_id")

        # Update the inventory level
        url = f"https://{self.shop_name}.myshopify.com/admin/api/{self.api_version}/inventory_levels/set.json"
        headers = {
            "X-Shopify-Access-Token": self.access_token,
            "Content-Type": "application/json",
        }
        data = {
            "location_id": location_id,
            "inventory_item_id": inventory_item_id,
            "available": quantity
        }
        response = requests.post(url, headers=headers, json=data, verify=self.verify_ssl)
        return response.status_code in (200, 201)

    def get_custom_collections(self):
        url = f"https://{self.shop_name}.myshopify.com/admin/api/{self.api_version}/custom_collections.json"
        headers = {
            "X-Shopify-Access-Token": self.access_token,
            "Content-Type": "application/json",
        }
        response = requests.get(url, headers=headers, verify=self.verify_ssl)
        collections = response.json().get("custom_collections", [])

        if not collections:
            print("No custom collections found!")
            return []
        return collections

    def get_collection_names_from_ids(self, collection_ids):
        """Get collection names from collection IDs

        Args:
            collection_ids: List of collection IDs

        Returns:
            List of collection names
        """
        all_collections = self.get_custom_collections()
        collection_names = []

        for collection_id in collection_ids:
            for collection in all_collections:
                if str(collection["id"]) == str(collection_id):
                    collection_names.append(collection["title"])
                    break

        return collection_names

    def get_sales_channels(self):
        """Get available sales channels for the shop

        Returns:
            List of sales channels
        """
        try:
            # First try to get the available sales channels using the GraphQL API
            # This is the most reliable way to get the actual sales channel IDs
            graphql_url = f"https://{self.shop_name}.myshopify.com/admin/api/{self.api_version}/graphql.json"
            headers = {
                "X-Shopify-Access-Token": self.access_token,
                "Content-Type": "application/json",
            }

            # GraphQL query to get all sales channels
            query = """
            {
              publications(first: 10) {
                edges {
                  node {
                    id
                    name
                    publishable
                  }
                }
              }
            }
            """

            response = requests.post(graphql_url, headers=headers, json={"query": query}, verify=self.verify_ssl)

            print(f"GraphQL sales channels response status: {response.status_code}")

            if response.status_code == 200:
                data = response.json()
                print(f"GraphQL response data: {data}")

                if "data" in data and "publications" in data["data"] and "edges" in data["data"]["publications"]:
                    edges = data["data"]["publications"]["edges"]
                    formatted_channels = []

                    for edge in edges:
                        node = edge.get("node", {})
                        channel_id = node.get("id", "")

                        # Extract the numeric ID from the GraphQL ID (format: gid://shopify/Publication/NUMERIC_ID)
                        if channel_id and "gid://" in channel_id:
                            parts = channel_id.split("/")
                            if len(parts) > 0:
                                numeric_id = parts[-1]
                                formatted_channels.append({
                                    "id": numeric_id,
                                    "name": node.get("name", f"Channel {numeric_id}")
                                })
                                print(f"Found sales channel: {node.get('name')} with ID: {numeric_id}")

                    if formatted_channels:
                        return formatted_channels

            # If GraphQL fails, try the REST API
            print("GraphQL approach failed, trying REST API")
            url = f"https://{self.shop_name}.myshopify.com/admin/api/{self.api_version}/publications.json"
            response = requests.get(url, headers=headers, verify=self.verify_ssl)

            print(f"REST API sales channels response status: {response.status_code}")

            if response.status_code == 200:
                publications = response.json().get("publications", [])

                if publications:
                    formatted_channels = []
                    for pub in publications:
                        pub_id = str(pub.get("id"))
                        pub_name = pub.get("name", f"Channel {pub_id}")
                        formatted_channels.append({
                            "id": pub_id,
                            "name": pub_name
                        })
                        print(f"Found sales channel via REST: {pub_name} with ID: {pub_id}")

                    if formatted_channels:
                        return formatted_channels

            # If both approaches fail, use the hardcoded values
            print("Both GraphQL and REST API approaches failed, using hardcoded values")
            return self._get_hardcoded_sales_channels()

        except Exception as e:
            print(f"Error getting sales channels: {str(e)}")
            return self._get_hardcoded_sales_channels()

    def _get_hardcoded_sales_channels(self):
        """Get hardcoded sales channels based on common Shopify channel IDs

        Returns:
            List of sales channels with actual IDs
        """
        # These are the actual publication IDs for common sales channels
        # These IDs are consistent across Shopify stores
        channels = [
            {"id": "1", "name": "Online Store"},
            {"id": "2", "name": "Buy Button"},
            {"id": "8", "name": "Point of Sale"},
            {"id": "4", "name": "Facebook"},
            {"id": "5", "name": "Instagram"}
        ]

        print(f"Using hardcoded sales channels: {channels}")
        return channels

    def _get_default_sales_channels(self):
        """Get default sales channels when API fails

        Returns:
            List of default sales channels
        """
        # Just use the hardcoded values
        return self._get_hardcoded_sales_channels()

    def create_metafield(self, product_id, namespace, key, value, value_type="string"):
        """Create a metafield for a product

        Args:
            product_id: The ID of the product
            namespace: The namespace for the metafield (e.g., 'custom')
            key: The key for the metafield (e.g., 'discogs_release_id')
            value: The value to store
            value_type: The type of the value (default: 'string')

        Returns:
            The created metafield data
        """
        url = f"https://{self.shop_name}.myshopify.com/admin/api/{self.api_version}/products/{product_id}/metafields.json"
        headers = {
            "X-Shopify-Access-Token": self.access_token,
            "Content-Type": "application/json",
        }
        metafield_data = {
            "metafield": {
                "namespace": namespace,
                "key": key,
                "value": value,
                "value_type": value_type
            }
        }
        response = requests.post(url, headers=headers, json=metafield_data, verify=self.verify_ssl)
        return response.json().get("metafield", {})

    def publish_product_to_sales_channel(self, product_id, publication_id):
        """Publish a product to a specific sales channel

        Args:
            product_id: The ID of the product
            publication_id: The ID of the publication (sales channel)

        Returns:
            True if successful, False otherwise
        """
        try:
            print(f"Attempting to publish product {product_id} to sales channel {publication_id}")

            # First, check if the product exists
            product_url = f"https://{self.shop_name}.myshopify.com/admin/api/{self.api_version}/products/{product_id}.json"
            headers = {
                "X-Shopify-Access-Token": self.access_token,
                "Content-Type": "application/json",
            }
            print(f"Checking if product exists: GET {product_url}")
            product_response = requests.get(product_url, headers=headers, verify=self.verify_ssl)

            print(f"Product check response status: {product_response.status_code}")

            if product_response.status_code != 200:
                print(f"Failed to get product {product_id}: {product_response.status_code} - {product_response.text}")
                return False

            product_data = product_response.json().get("product", {})
            print(f"Product exists with title: {product_data.get('title')}")

            # Special handling for Point of Sale (publication_id = 8)
            if publication_id == "8" or publication_id == 8:
                print("Special handling for Point of Sale channel")
                return self._publish_to_point_of_sale(product_id)

            # Try different approaches to publish the product to the sales channel

            # Approach 1: Use the publications endpoint with GraphQL
            graphql_url = f"https://{self.shop_name}.myshopify.com/admin/api/{self.api_version}/graphql.json"

            # GraphQL mutation to publish a product to a specific publication
            mutation = """
            mutation publishProductToPublication($productId: ID!, $publicationId: ID!) {
              publishablePublish(id: $productId, input: {publicationId: $publicationId}) {
                publishable {
                  id
                }
                userErrors {
                  field
                  message
                }
              }
            }
            """

            # Convert IDs to GraphQL format
            gql_product_id = f"gid://shopify/Product/{product_id}"
            gql_publication_id = f"gid://shopify/Publication/{publication_id}"

            variables = {
                "productId": gql_product_id,
                "publicationId": gql_publication_id
            }

            print(f"GraphQL approach: POST {graphql_url}")
            print(f"GraphQL variables: {variables}")

            graphql_response = requests.post(
                graphql_url,
                headers=headers,
                json={"query": mutation, "variables": variables},
                verify=self.verify_ssl
            )

            print(f"GraphQL response status: {graphql_response.status_code}")
            if graphql_response.status_code == 200:
                data = graphql_response.json()
                print(f"GraphQL response data: {data}")

                if "data" in data and "publishablePublish" in data["data"]:
                    user_errors = data["data"]["publishablePublish"].get("userErrors", [])
                    if not user_errors:
                        print("Successfully published product using GraphQL")
                        return True
                    else:
                        print(f"GraphQL user errors: {user_errors}")

            # Approach 2: Use the REST API publications endpoint
            url1 = f"https://{self.shop_name}.myshopify.com/admin/api/{self.api_version}/products/{product_id}/publications.json"
            publication_data1 = {
                "publication": {
                    "publication_id": publication_id,
                    "published": True
                }
            }

            print(f"REST API approach 1: POST {url1}")
            print(f"Request data: {publication_data1}")

            response1 = requests.post(url1, headers=headers, json=publication_data1, verify=self.verify_ssl)

            print(f"REST API approach 1 response status: {response1.status_code}")
            if response1.status_code in (200, 201):
                print(f"REST API approach 1 successful: {response1.json()}")
                return True
            else:
                print(f"REST API approach 1 failed: {response1.text}")

            # Approach 3: Use the direct publication endpoint
            url2 = f"https://{self.shop_name}.myshopify.com/admin/api/{self.api_version}/publications/{publication_id}/products/{product_id}.json"

            print(f"REST API approach 2: PUT {url2}")

            response2 = requests.put(url2, headers=headers, verify=self.verify_ssl)

            print(f"REST API approach 2 response status: {response2.status_code}")
            if response2.status_code in (200, 201):
                print(f"REST API approach 2 successful: {response2.json() if response2.text else 'No response body'}")
                return True
            else:
                print(f"REST API approach 2 failed: {response2.text}")

            # If all approaches failed, return False
            print("All approaches failed to publish product to sales channel")
            return False
        except Exception as e:
            print(f"Error publishing product {product_id} to sales channel {publication_id}: {str(e)}")
            return False

    def _publish_to_point_of_sale(self, product_id):
        """Special handling for publishing to Point of Sale

        Args:
            product_id: The ID of the product

        Returns:
            True if successful, False otherwise
        """
        try:
            print(f"Publishing product {product_id} to Point of Sale")

            # For Point of Sale, we need to use a different approach
            # We need to update the product with a specific tag
            product_url = f"https://{self.shop_name}.myshopify.com/admin/api/{self.api_version}/products/{product_id}.json"
            headers = {
                "X-Shopify-Access-Token": self.access_token,
                "Content-Type": "application/json",
            }

            # First, get the current product data
            product_response = requests.get(product_url, headers=headers, verify=self.verify_ssl)

            if product_response.status_code != 200:
                print(f"Failed to get product for POS update: {product_response.status_code} - {product_response.text}")
                return False

            product_data = product_response.json().get("product", {})
            current_tags = product_data.get("tags", "")

            # Add the POS tag if it's not already there
            tags = current_tags.split(", ") if current_tags else []
            if "pos" not in tags:
                tags.append("pos")

            # Update the product with the new tags
            update_data = {
                "product": {
                    "id": product_id,
                    "tags": ", ".join(tags),
                    "published_scope": "global"  # Ensure it's published globally
                }
            }

            print(f"Updating product for POS: PUT {product_url}")
            print(f"Update data: {update_data}")

            update_response = requests.put(product_url, headers=headers, json=update_data, verify=self.verify_ssl)

            print(f"POS update response status: {update_response.status_code}")
            if update_response.status_code in (200, 201):
                print(f"Successfully published product to Point of Sale")
                return True
            else:
                print(f"Failed to publish product to Point of Sale: {update_response.text}")
                return False

        except Exception as e:
            print(f"Error publishing product to Point of Sale: {str(e)}")
            return False

    def _add_product_to_publication(self, product_id, publication_id):
        """Add a product to a publication (sales channel)

        Args:
            product_id: The ID of the product
            publication_id: The ID of the publication (sales channel)

        Returns:
            True if successful, False otherwise
        """
        try:
            print(f"Attempting to add product {product_id} to publication {publication_id}")

            # Use the collect endpoint to add the product to the publication
            url = f"https://{self.shop_name}.myshopify.com/admin/api/{self.api_version}/collects.json"
            headers = {
                "X-Shopify-Access-Token": self.access_token,
                "Content-Type": "application/json",
            }
            collect_data = {
                "collect": {
                    "product_id": product_id,
                    "collection_id": publication_id
                }
            }

            print(f"Adding product to publication: POST {url}")
            print(f"Request data: {collect_data}")

            response = requests.post(url, headers=headers, json=collect_data, verify=self.verify_ssl)

            print(f"Add to publication response status: {response.status_code}")
            if response.status_code in (200, 201):
                print(f"Successfully added product to publication: {response.json()}")
                return True
            else:
                print(f"Failed to add product to publication: {response.text}")
                return False
        except Exception as e:
            print(f"Error adding product to publication: {str(e)}")
            return False


# Usage example:
# # Replace 'SHOPIFY_SHOP_NAME' and 'SHOPIFY_TOKEN' with your actual Shopify shop name and access token.
# shopify_client = ShopifyAPIClient('YOUR_SHOP_NAME', 'YOUR_ACCESS_TOKEN')

# # Create a new Shopify product
# product_data = {
#     # ... provide product data as a dictionary ...
# }
# collection_ids = [1, 2, 3]  # Replace with the collection IDs you want to add the product to

# product_id = shopify_client.create_shopify_product(product_data, collection_ids)

# # Update a product image
# product_id = 123  # Replace with the actual product ID
# thumb_url = 'https://example.com/thumb.jpg'  # Replace with the image URL you want to use
# response = shopify_client.update_shopify_product_image(product_id, thumb_url)
# print(response)
