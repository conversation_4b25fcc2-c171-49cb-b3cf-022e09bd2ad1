import json
import requests
import warnings
import logging
from pydantic import BaseModel, Field
from enum import Enum
from typing import Optional, Dict, Any

# Import our error handling and resilience utilities
from utils.error_handling import (
    DiscogsAPIError,
    ErrorCategory,
    RetryConfig,
    retry_with_backoff,
    get_discogs_circuit_breaker,
    handle_api_error,
    log_error_with_context
)
from utils.api_resilience import ResilientHTTPClient, RateLimitConfig, ConnectionConfig


class Condition(Enum):
    MINT = "Mint (M)"
    NEAR_MINT = "Near Mint (NM or M-)"
    VERY_GOOD_PLUS = "Very Good Plus (VG+)"
    VERY_GOOD = "Very Good (VG)"
    GOOD_PLUS = "Good Plus (G+)"
    GOOD = "Good (G)"
    FAIR = "Fair (F)"
    POOR = "Poor (P)"

    def to_string(self):
        return f"{self.name}: {self.value}"

    def to_dict(self):
        return {"name": self.name, "value": self.value}


class DiscogsListing(BaseModel):
    release_id: int = Field(
        description="The ID of the release you are posting (required)", example=1
    )
    condition: Condition = Field(
        description="The condition of the release you are posting (required)",
        example="Fair (F)",
    )
    sleeve_condition: Condition = Field(
        description="The condition of the sleeve of the item you are posting",
        example="Fair (F)",
    )
    price: float = Field(
        description="The price of the item (in the seller’s currency) (required)",
        example=10.00,
    )
    comments: str = Field(
        description="Any remarks about the item that will be displayed to buyers (optional)",
        example="This item is wonderful",
    )
    allow_offers: bool = Field(
        description="Whether or not to allow buyers to make offers on the item (optional)",
        example=True,
        default=None,
    )
    status: str = Field(
        description="The status of the listing (required)",
        default="For Sale",
        example="Draft",
        enum=["For Sale", "Draft"],
    )
    external_id: str = Field(
        description="A freeform field that can be used for the seller’s own reference (optional)",
        default=None,
    )
    location: str = Field(
        description="A freeform field that is intended to help identify an item’s physical storage location",
        example="Bin 14",
    )
    weight: float = Field(
        description="The weight, in grams, of this listing, for the purpose of calculating shipping (optional)",
        example=10.00,
        default=None,
    )
    format_quantity: float = Field(
        description="The number of items this listing counts as, for the purpose of calculating shipping (optional)",
        example=10.00,
        default=None,
    )

    def to_dict(self):
        return {
            "release_id": self.release_id,
            "condition": self.condition.value,
            "sleeve_condition": self.sleeve_condition.value,
            "price": self.price,
            "comments": self.comments,
            "allow_offers": self.allow_offers,
            "status": self.status,
            "external_id": self.external_id,
            "location": self.location,
            "weight": self.weight,
            "format_quantity": self.format_quantity,
        }

    def to_string(self):
        return (
            f"Release ID: {self.release_id}\n"
            f"Condition: {self.condition.value}\n"
            f"Sleeve Condition: {self.sleeve_condition.value}\n"
            f"Price: {self.price}\n"
            f"Comments: {self.comments}\n"
            f"Allow Offers: {self.allow_offers}\n"
            f"Status: {self.status}\n"
            f"External ID: {self.external_id}\n"
            f"Location: {self.location}\n"
            f"Weight: {self.weight}\n"
            f"Format Quantity: {self.format_quantity}"
        )


class DiscogsAPIClient:
    def __init__(self, token=None, verify_ssl=False):
        self.base_url = "https://api.discogs.com"
        self.token = token
        self.verify_ssl = verify_ssl
        self.logger = logging.getLogger(__name__)

        # Initialize resilient HTTP client
        rate_limit_config = RateLimitConfig(
            requests_per_minute=60,  # Discogs allows 60 requests per minute
            requests_per_second=1,   # Be conservative
            burst_limit=3
        )

        connection_config = ConnectionConfig(
            timeout=30,
            connect_timeout=10,
            read_timeout=20,
            max_retries=3
        )

        retry_config = RetryConfig(
            max_retries=3,
            base_delay=1.0,
            max_delay=30.0
        )

        self.http_client = ResilientHTTPClient(
            base_url=self.base_url,
            rate_limit_config=rate_limit_config,
            connection_config=connection_config,
            retry_config=retry_config,
            service_name="discogs"
        )

        if not verify_ssl:
            # Suppress only the InsecureRequestWarning
            warnings.filterwarnings('ignore', 'Unverified HTTPS request is being made.*')

    def _make_request(self, method: str, endpoint: str, params=None, headers=None) -> Dict[str, Any]:
        """Make a request with error handling and resilience features"""
        headers = headers or {}
        if self.token:
            headers["Authorization"] = f"Discogs token={self.token}"

        # Use circuit breaker for protection
        circuit_breaker = get_discogs_circuit_breaker()

        try:
            def make_api_call():
                kwargs = {
                    'headers': headers,
                    'verify': self.verify_ssl
                }

                if method == "POST":
                    kwargs['data'] = params
                else:
                    kwargs['params'] = params

                response = self.http_client.request(method, endpoint, **kwargs)
                return response.json()

            return circuit_breaker.call(make_api_call)

        except Exception as e:
            # Convert to DiscogsAPIError with proper categorization
            error = handle_api_error(e, f"Discogs {method} {endpoint}")
            log_error_with_context(error, self.logger)
            raise error

    @retry_with_backoff(RetryConfig(max_retries=2, base_delay=0.5))
    def search_release(self, query: str, page: int = 1, per_page: int = 10) -> Dict[str, Any]:
        """Search for releases on Discogs"""
        params = {"q": query, "page": page, "per_page": per_page}
        endpoint = "database/search"

        try:
            return self._make_request("GET", endpoint, params=params)
        except Exception as e:
            self.logger.error(f"Failed to search releases for query '{query}': {str(e)}")
            raise

    @retry_with_backoff(RetryConfig(max_retries=3, base_delay=1.0))
    def create_listing(self, listing) -> Dict[str, Any]:
        """Create a new marketplace listing"""
        data = json.dumps(
            {
                "release_id": listing.release_id,
                "condition": listing.condition.value,
                "sleeve_condition": listing.sleeve_condition.value,
                "price": listing.price,
                "comments": listing.comments,
                "allow_offers": listing.allow_offers,
                "status": listing.status,
                "external_id": listing.external_id,
                "location": listing.location,
                "weight": listing.weight,
                "format_quantity": listing.format_quantity,
            }
        )
        endpoint = "marketplace/listings"
        headers = {"Content-Type": "application/json"}

        try:
            result = self._make_request("POST", endpoint, params=data, headers=headers)
            self.logger.info(f"Successfully created Discogs listing for release {listing.release_id}")
            return result
        except Exception as e:
            self.logger.error(f"Failed to create Discogs listing for release {listing.release_id}: {str(e)}")
            raise

    @retry_with_backoff(RetryConfig(max_retries=2, base_delay=0.5))
    def get_release(self, discogs_id: str) -> Dict[str, Any]:
        """Get release information by Discogs ID"""
        endpoint = f"releases/{discogs_id}"

        try:
            return self._make_request("GET", endpoint)
        except Exception as e:
            self.logger.error(f"Failed to get release {discogs_id}: {str(e)}")
            raise

    @retry_with_backoff(RetryConfig(max_retries=2, base_delay=0.5))
    def get_marketplace_listing(self, listing_id: str) -> Dict[str, Any]:
        """Get marketplace listing by ID"""
        endpoint = f"marketplace/listings/{listing_id}"

        try:
            return self._make_request("GET", endpoint)
        except Exception as e:
            self.logger.error(f"Failed to get marketplace listing {listing_id}: {str(e)}")
            raise

    @retry_with_backoff(RetryConfig(max_retries=3, base_delay=1.0))
    def delete_listing(self, listing_id: str) -> Dict[str, Any]:
        """Delete a marketplace listing"""
        endpoint = f"marketplace/listings/{listing_id}"

        try:
            result = self._make_request("DELETE", endpoint)
            self.logger.info(f"Successfully deleted Discogs listing {listing_id}")
            return result
        except Exception as e:
            self.logger.error(f"Failed to delete Discogs listing {listing_id}: {str(e)}")
            raise

    @retry_with_backoff(RetryConfig(max_retries=3, base_delay=1.0))
    def update_listing(self, listing_id: str, data: str) -> Dict[str, Any]:
        """Update a marketplace listing"""
        endpoint = f"marketplace/listings/{listing_id}"
        headers = {"Content-Type": "application/json"}

        try:
            result = self._make_request("POST", endpoint, params=data, headers=headers)
            self.logger.info(f"Successfully updated Discogs listing {listing_id}")
            return result
        except Exception as e:
            self.logger.error(f"Failed to update Discogs listing {listing_id}: {str(e)}")
            raise

    def health_check(self) -> bool:
        """Check if Discogs API is healthy"""
        return self.http_client.health_check()

    def is_healthy(self) -> bool:
        """Check if Discogs API is currently healthy"""
        return self.http_client.is_healthy()


# Example usage:
# client = DiscogsAPIClient(token="YOUR_API_TOKEN")
# results = client.search_release("Pink Floyd Dark Side of the Moon")
# print(results)
