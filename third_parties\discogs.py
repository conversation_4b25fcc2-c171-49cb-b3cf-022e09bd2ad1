import json
import requests
import warnings
from pydantic import BaseModel, Field
from enum import Enum


class Condition(Enum):
    MINT = "Mint (M)"
    NEAR_MINT = "Near Mint (NM or M-)"
    VERY_GOOD_PLUS = "Very Good Plus (VG+)"
    VERY_GOOD = "Very Good (VG)"
    GOOD_PLUS = "Good Plus (G+)"
    GOOD = "Good (G)"
    FAIR = "Fair (F)"
    POOR = "Poor (P)"

    def to_string(self):
        return f"{self.name}: {self.value}"

    def to_dict(self):
        return {"name": self.name, "value": self.value}


class DiscogsListing(BaseModel):
    release_id: int = Field(
        description="The ID of the release you are posting (required)", example=1
    )
    condition: Condition = Field(
        description="The condition of the release you are posting (required)",
        example="Fair (F)",
    )
    sleeve_condition: Condition = Field(
        description="The condition of the sleeve of the item you are posting",
        example="Fair (F)",
    )
    price: float = Field(
        description="The price of the item (in the seller’s currency) (required)",
        example=10.00,
    )
    comments: str = Field(
        description="Any remarks about the item that will be displayed to buyers (optional)",
        example="This item is wonderful",
    )
    allow_offers: bool = Field(
        description="Whether or not to allow buyers to make offers on the item (optional)",
        example=True,
        default=None,
    )
    status: str = Field(
        description="The status of the listing (required)",
        default="For Sale",
        example="Draft",
        enum=["For Sale", "Draft"],
    )
    external_id: str = Field(
        description="A freeform field that can be used for the seller’s own reference (optional)",
        default=None,
    )
    location: str = Field(
        description="A freeform field that is intended to help identify an item’s physical storage location",
        example="Bin 14",
    )
    weight: float = Field(
        description="The weight, in grams, of this listing, for the purpose of calculating shipping (optional)",
        example=10.00,
        default=None,
    )
    format_quantity: float = Field(
        description="The number of items this listing counts as, for the purpose of calculating shipping (optional)",
        example=10.00,
        default=None,
    )

    def to_dict(self):
        return {
            "release_id": self.release_id,
            "condition": self.condition.value,
            "sleeve_condition": self.sleeve_condition.value,
            "price": self.price,
            "comments": self.comments,
            "allow_offers": self.allow_offers,
            "status": self.status,
            "external_id": self.external_id,
            "location": self.location,
            "weight": self.weight,
            "format_quantity": self.format_quantity,
        }

    def to_string(self):
        return (
            f"Release ID: {self.release_id}\n"
            f"Condition: {self.condition.value}\n"
            f"Sleeve Condition: {self.sleeve_condition.value}\n"
            f"Price: {self.price}\n"
            f"Comments: {self.comments}\n"
            f"Allow Offers: {self.allow_offers}\n"
            f"Status: {self.status}\n"
            f"External ID: {self.external_id}\n"
            f"Location: {self.location}\n"
            f"Weight: {self.weight}\n"
            f"Format Quantity: {self.format_quantity}"
        )


class DiscogsAPIClient:
    def __init__(self, token=None, verify_ssl=False):
        self.base_url = "https://api.discogs.com"
        self.token = token  # You can set your API token here if required
        self.verify_ssl = verify_ssl

        if not verify_ssl:
            # Suppress only the InsecureRequestWarning
            warnings.filterwarnings('ignore', 'Unverified HTTPS request is being made.*')

    def _make_request(self, method, endpoint, params=None, headers=None):
        url = f"{self.base_url}/{endpoint}"
        headers = headers or {}
        if self.token:
            headers["Authorization"] = f"Discogs token={self.token}"

        if method == "POST":
            response = requests.request(
                method=method, url=url, data=params, headers=headers, verify=self.verify_ssl
            )
        else:
            response = requests.request(
                method=method, url=url, params=params, headers=headers, verify=self.verify_ssl
            )

        if 200 <= response.status_code < 300:
            return response.json()
        else:
            # print(response.text)
            response.raise_for_status()

    def search_release(self, query, page=1, per_page=10):
        params = {"q": query, "page": page, "per_page": per_page}
        endpoint = "database/search"
        return self._make_request("GET", endpoint, params=params)

    def create_listing(self, listing):
        data = json.dumps(
            {
                "release_id": listing.release_id,
                "condition": listing.condition.value,
                "sleeve_condition": listing.sleeve_condition.value,
                "price": listing.price,
                "comments": listing.comments,
                "allow_offers": listing.allow_offers,
                "status": listing.status,
                "external_id": listing.external_id,
                "location": listing.location,
                "weight": listing.weight,
                "format_quantity": listing.format_quantity,
            }
        )
        endpoint = "marketplace/listings"
        headers = {}
        headers["Content-Type"] = "application/json"
        return self._make_request("POST", endpoint, params=data, headers=headers)

    def get_release(self, discogs_id):
        endpoint = f"releases/{discogs_id}"
        return self._make_request("GET", endpoint)

    def get_marketplace_listing(self, listing_id):
        endpoint = f"marketplace/listings/{listing_id}"
        return self._make_request("GET", endpoint)

    def delete_listing(self, listing_id):
        """Delete a marketplace listing"""
        endpoint = f"marketplace/listings/{listing_id}"
        return self._make_request("DELETE", endpoint)

    def update_listing(self, listing_id, data):
        """Update a marketplace listing"""
        endpoint = f"marketplace/listings/{listing_id}"
        headers = {"Content-Type": "application/json"}
        return self._make_request("POST", endpoint, params=data, headers=headers)


# Example usage:
# client = DiscogsAPIClient(token="YOUR_API_TOKEN")
# results = client.search_release("Pink Floyd Dark Side of the Moon")
# print(results)
