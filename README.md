# Stellar Sync

Stellar Sync is a powerful web application that streamlines the process of managing record listings across Discogs and Shopify platforms. Designed for record stores and collectors, it allows you to create, manage, and synchronize listings between these platforms with just a few clicks.

## Features

- **Cross-Platform Integration**: Seamlessly create listings on both Discogs and Shopify simultaneously
- **Bulk Operations**: Add multiple items with the same details in one operation
- **Custom Collections**: Organize your Shopify products into collections
- **Automatic Metadata**: Pull release information directly from Discogs, including:
  - Artist and album details
  - Track listings
  - Release dates
  - High-resolution cover art (full-sized images, not thumbnails)
- **Inventory Tracking**: Track quantities and bin locations for physical inventory
- **Barcode Support**: Add product barcodes (UPC/EAN) to Shopify listings
- **Discogs Metadata Storage**: Store Discogs release IDs in Shopify product metafields for reference
- **Custom Product Descriptions**: Enter your own product descriptions with automatically appended tracklists
  - Tracklisting background matches the rest of the product description (no white background)
- **New/Used Condition**: Clearly indicate whether products are new or used in the product title
- **Dark Mode UI**: Enjoy a visually comfortable interface with dark mode styling
- **Dynamic UI**: Form adapts based on user selections, showing relevant fields only when needed
- **Listing Logs**: Comprehensive logging of all created listings with Discogs release IDs
- **Version Display**: Application version number shown at the bottom of the UI
- **Branding**: Stellar Records copyright notice displayed at the bottom of the UI

## Getting Started

### Prerequisites

- Docker (recommended) or Python 3.11+
- Discogs API token
- Shopify API token and shop name

### Installation

#### Using Docker (Recommended)

1. Clone this repository
2. Create a `.env` file based on `.env.example` with your API credentials
3. Build the Docker image:
   ```
   docker build -t stellar-records .
   ```
4. Run the container:
   ```
   docker run -p 5000:5000 stellar-records
   ```

Alternatively, use the provided scripts:
- Windows: Run `build_docker.bat` followed by `start.bat`

#### Docker with Persistent Storage

For a containerized deployment with persistent database and logs:

1. Clone this repository
2. Build and start the container using Docker Compose:
   ```bash
   docker-compose up -d
   ```

This will:
- Create persistent volumes for the database and logs
- Start the application on port 5000
- Ensure data is preserved across container restarts

##### Manual Docker Setup with Volumes

If you prefer to run without Docker Compose:

1. Create Docker volumes for persistent storage:
   ```bash
   docker volume create stellar-sync-db
   docker volume create stellar-sync-logs
   ```

2. Build the Docker image:
   ```bash
   docker build -t stellar-records .
   ```

3. Run the container with volume mappings:
   ```bash
   docker run -d \
     --name stellar-sync \
     -p 5000:5000 \
     -v stellar-sync-db:/app/instance \
     -v stellar-sync-logs:/app/logs \
     stellar-records
   ```

##### Using Host Directories Instead of Volumes

If you prefer to use host directories instead of Docker volumes:

1. Create directories on your host machine:
   ```bash
   mkdir -p /path/to/host/db
   mkdir -p /path/to/host/logs
   ```

2. Run with host directory mappings:
   ```bash
   docker run -d \
     --name stellar-sync \
     -p 5000:5000 \
     -v /path/to/host/db:/app/instance \
     -v /path/to/host/logs:/app/logs \
     stellar-records
   ```

#### Manual Installation

1. Clone this repository
2. Create a `.env` file based on `.env.example` with your API credentials
3. Install dependencies:
   ```
   pipenv install
   ```
4. Run the application:
   ```
   pipenv run python api.py
   ```

## Usage

1. Open your browser and navigate to `http://localhost:5000`
2. Enter the Discogs Release ID for the record you want to list
3. Select whether to create a listing on Discogs (optional)
   - When checked, a "Discogs Details" section will appear with fields for bin location and comments
4. Enter media and sleeve condition details
5. Set the price and quantity
6. Select whether the product is New or Used (will be displayed in the product title)
7. Enter a barcode (UPC/EAN) if available
8. Enter a custom product description (the tracklist will be automatically appended)
9. Select Shopify collections to add the product to
10. If creating a Discogs listing, enter bin location and comments in the Discogs Details section
11. Submit the form to create listings on both platforms

The application will:
- Create a Shopify product with the artist name first, followed by the title and condition (e.g., "Pink Floyd - Dark Side of the Moon - Used")
- Use your custom description with the tracklist appended in a matching background style
- Display full-sized album artwork from Discogs instead of thumbnails
- Store the barcode in the Shopify product data if provided
- Store the Discogs release ID in Shopify product metafields for reference
- Optionally create a matching listing on Discogs
- Log all new listings with Discogs release ID for future reference
- Display the current application version and copyright notice at the bottom of the UI

## Listings Log

Stellar Sync includes a comprehensive logging system that records all listing activities:

1. Access the Listings Log by clicking the "Listings Log" button at the top of the main interface
2. View a chronological list of all listings created through the application
3. Each log entry includes:
   - Timestamp
   - Discogs Release ID
   - Artist and title information
   - Price and condition
   - Platform (Discogs, Shopify, or both)
   - Listing IDs for reference
4. Filter log entries by release ID, artist, or title using the search box
5. Refresh the log to see the latest entries

This feature helps you keep track of your listing history and provides a reference for all items you've added to your inventory.

## Architecture

Stellar Sync consists of:

- **Flask Web Application**: Provides the user interface and API endpoints
- **Discogs Client**: Interfaces with the Discogs API to fetch release data and create listings
- **Shopify Client**: Manages product creation and updates on your Shopify store
- **Integration Layer**: Coordinates operations between the two platforms
- **Logging System**: Records all listing activities in a CockroachDB database

## Environment Variables

Stellar Sync uses environment variables for configuration. You can set these in a `.env` file or directly in your environment.

### Required Environment Variables

- `DATABASE_URL`: Connection string for your CockroachDB instance
  - Format: `postgresql://username:password@host:port/stellarsync?sslmode=require`
  - The application is configured to use a database named 'stellarsync'
  - Using `sslmode=require` is necessary for CockroachDB Cloud connections as the server requires encryption

### Optional Environment Variables

- `DB_NAME`: The name of the database to use (default: `stellarsyncQA`). Note that CockroachDB converts database names to lowercase automatically.
- `DISCOGS_TOKEN`: Your Discogs API token
- `SHOPIFY_TOKEN`: Your Shopify API token
- `SHOPIFY_SHOP_NAME`: Your Shopify shop name
- `INSTANCE_PATH`: Path to store instance data (default: `/app/instance`)
- `LOGS_PATH`: Path to store logs (default: `/app/logs`)

### Using Docker Compose

When using Docker Compose, you can set environment variables in the `docker-compose.yml` file or in a `.env` file in the same directory.

Example `.env` file:
```
COCKROACH_DB_URL=postgresql://username:password@host:port/stellarsync?sslmode=disable
DB_NAME=stellarsync
DISCOGS_TOKEN=your_discogs_token
SHOPIFY_TOKEN=your_shopify_token
SHOPIFY_SHOP_NAME=your_shop_name
INSTANCE_PATH=/app/instance
LOGS_PATH=/app/logs
```

## Future Roadmap

- Automatic search on Discogs and selection from results (eliminating the need for a separate Discogs window)
- Price suggestions from Discogs marketplace data
- AI-assisted product description generation
- Batch import/export functionality
- Sales analytics and reporting
- Theme customization options
- Barcode scanning for quick Discogs lookup
- Integration with additional marketplaces
- Mobile-optimized interface
- Complete two-way inventory synchronization between platforms (in progress)

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request
