#!/bin/bash

# Convert DB_NAME to lowercase (CockroachDB converts names to lowercase)
if [ -n "${DB_NAME}" ]; then
    DB_NAME_LOWER=$(echo ${DB_NAME} | tr '[:upper:]' '[:lower:]')
else
    DB_NAME="stellarsyncQA"
    DB_NAME_LOWER="stellarsyncqa"
fi

export DB_NAME
export DB_NAME_LOWER

echo "Using database name: ${DB_NAME} (lowercase: ${DB_NAME_LOWER})"

# Test database connection
echo "Testing database connection..."
python test_db_connection.py

# Create the database
echo "Creating database ${DB_NAME} if it doesn't exist"
python create_db.py

# Create database tables
echo "Creating database tables"
python -c "
import os
os.environ['FLASK_APP'] = 'api.py'
from api import app
from models import db
from log_models import ListingLog, ErrorLog
with app.app_context():
    db.create_all()
"

# Import existing logs if this is the first run
if [ ! -f /app/logs/.imported ]; then
  echo "Importing existing logs"
  python import_logs.py
  touch /app/logs/.imported
fi

# Run the Flask application
echo "Starting Flask application"
python api.py
