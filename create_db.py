import os
import sys
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
import re
from sqlalchemy.dialects.postgresql.base import PGDialect

# Monkey patch the PostgreSQL dialect to handle CockroachDB version string
def _get_server_version_info(self, connection):
    v = connection.exec_driver_sql("SELECT version()").scalar()
    m = re.match(r"CockroachDB.*v(\d+)\.(\d+)\.(\d+)", v)
    if m:
        return (int(m.group(1)), int(m.group(2)), int(m.group(3)))
    else:
        # If we can't parse the version, return a default
        return (19, 2, 0)  # Default to a compatible version

# Apply the monkey patch
PGDialect._get_server_version_info = _get_server_version_info

def parse_connection_string(conn_string):
    """Parse a PostgreSQL connection string into components"""
    # Regular expression to match the connection string format
    pattern = r'postgresql://([^:]+):([^@]+)@([^:]+):(\d+)/([^?]+)(\?.*)?'
    match = re.match(pattern, conn_string)

    if not match:
        raise ValueError("Invalid connection string format")

    username, password, host, port, database, params = match.groups()

    # Ensure we're using sslmode=require
    if params:
        # Check if sslmode is already in the params
        if 'sslmode=' in params:
            # Replace the existing sslmode with require
            params = re.sub(r'sslmode=[^&]+', 'sslmode=require', params)
        else:
            # Add sslmode=require to the params
            params += '&sslmode=require'
    else:
        params = '?sslmode=require'

    return {
        'username': username,
        'password': password,
        'host': host,
        'port': port,
        'database': database,
        'params': params
    }

def create_database():
    """Create the database in CockroachDB using the DB_NAME environment variable"""
    # Get the connection string and database name from environment variables
    conn_string = os.getenv('DATABASE_URL')
    db_name = os.getenv('DB_NAME', 'stellarsyncQA')
    db_name_lower = os.getenv('DB_NAME_LOWER', db_name.lower())

    if not conn_string:
        print("ERROR: DATABASE_URL environment variable is not set.")
        return False

    try:
        # Extract connection parts from the connection string
        # We'll use a simpler approach to avoid issues with the regex
        conn_parts = {}

        # Find the protocol, username, password, host, port
        auth_part = conn_string.split('@')[0]
        protocol = auth_part.split('://')[0]
        username_password = auth_part.split('://')[1]
        username = username_password.split(':')[0]
        password = username_password.split(':')[1]

        # Find host and port
        host_port = conn_string.split('@')[1].split('/')[0]
        host = host_port.split(':')[0]
        port = host_port.split(':')[1]

        # Get params if any
        params = ""
        if '?' in conn_string:
            params = '?' + conn_string.split('?')[1]

        print(f"Connecting to CockroachDB at {host}:{port}...")

        # Connect to the default database first
        default_conn_string = f"{protocol}://{username}:{password}@{host}:{port}/defaultdb{params}"
        conn = psycopg2.connect(default_conn_string)
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()

        # Check if the database already exists (case insensitive)
        cursor.execute("SELECT datname FROM pg_database")
        existing_dbs = [row[0].lower() for row in cursor.fetchall()]

        if db_name_lower in existing_dbs:
            print(f"Database '{db_name}' already exists.")
        else:
            # Create the database
            print(f"Creating database '{db_name}'...")
            cursor.execute(f"CREATE DATABASE \"{db_name}\"")
            print(f"Database '{db_name}' created successfully.")

        cursor.close()
        conn.close()

        # Now connect to the database to verify it works
        db_conn_string = f"{protocol}://{username}:{password}@{host}:{port}/{db_name_lower}{params}"
        print(f"Connecting to database: {db_conn_string}")
        conn = psycopg2.connect(db_conn_string)
        cursor = conn.cursor()

        # Check if we can execute a simple query
        cursor.execute("SELECT 1")
        result = cursor.fetchone()
        if result and result[0] == 1:
            print(f"Successfully connected to '{db_name}' database.")

        cursor.close()
        conn.close()

        # Make sure the lowercase database name is available to other scripts
        os.environ['DB_NAME_LOWER'] = db_name_lower
        return True
    except Exception as e:
        print(f"Error creating database: {str(e)}")
        return False

if __name__ == "__main__":
    success = create_database()
    sys.exit(0 if success else 1)
