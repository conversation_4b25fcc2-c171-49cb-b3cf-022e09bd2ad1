from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
import enum

db = SQLAlchemy()

class PlatformType(enum.Enum):
    DISCOGS = "discogs"
    SHOPIFY = "shopify"

class SyncStatus(enum.Enum):
    PENDING = "pending"
    SYNCED = "synced"
    FAILED = "failed"

class InventoryItem(db.Model):
    """Model for tracking inventory across platforms"""
    id = db.Column(db.Integer, primary_key=True)
    
    # Common fields
    release_id = db.Column(db.String(50), nullable=False)  # Discogs release ID
    title = db.Column(db.String(255), nullable=False)
    artist = db.Column(db.String(255), nullable=False)
    condition = db.Column(db.String(50), nullable=False)
    price = db.Column(db.Float, nullable=False)
    quantity = db.Column(db.Integer, default=0)
    
    # Platform-specific IDs
    discogs_listing_id = db.Column(db.String(50), nullable=True)
    shopify_product_id = db.Column(db.String(50), nullable=True)
    shopify_variant_id = db.Column(db.String(50), nullable=True)
    
    # Sync status
    last_sync = db.Column(db.DateTime, default=datetime.utcnow)
    sync_status = db.Column(db.Enum(SyncStatus), default=SyncStatus.PENDING)
    
    # Metadata
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f"<InventoryItem {self.artist} - {self.title} ({self.quantity} available)>"

class SyncLog(db.Model):
    """Model for tracking sync operations"""
    id = db.Column(db.Integer, primary_key=True)
    inventory_item_id = db.Column(db.Integer, db.ForeignKey('inventory_item.id'), nullable=False)
    platform = db.Column(db.Enum(PlatformType), nullable=False)
    action = db.Column(db.String(50), nullable=False)  # e.g., "update_quantity", "create", "delete"
    status = db.Column(db.Enum(SyncStatus), default=SyncStatus.PENDING)
    message = db.Column(db.Text, nullable=True)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationship
    inventory_item = db.relationship('InventoryItem', backref=db.backref('sync_logs', lazy=True))
    
    def __repr__(self):
        return f"<SyncLog {self.platform.value} {self.action} {self.status.value}>"
