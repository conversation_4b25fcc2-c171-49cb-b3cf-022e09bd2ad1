# Example environment variables file
# Copy this file to .env and fill in your values

# Database connection string for CockroachDB
# Format: postgresql://username:password@host:port/database?sslmode=require
# Note: CockroachDB Cloud requires sslmode=require
COCKROACH_DB_URL=postgresql://username:password@host:port/stellarsync?sslmode=require

# Database name to use (will be used in the connection string if not specified there)
DB_NAME=stellarsync

# API Keys
DISCOGS_TOKEN=your_discogs_token
SHOPIFY_TOKEN=your_shopify_token
SHOPIFY_SHOP_NAME=your_shop_name

# Paths
INSTANCE_PATH=/app/instance
LOGS_PATH=/app/logs
