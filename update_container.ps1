# PowerShell script to update Stellar Sync container

# Function to display colorful messages
function Write-ColorOutput {
    param (
        [Parameter(Mandatory=$false)]
        [string]$Message = " ",

        [Parameter(Mandatory=$false)]
        [string]$ForegroundColor = "White"
    )

    Write-Host $Message -ForegroundColor $ForegroundColor
}

# Display header
Write-ColorOutput "====================================" "Cyan"
Write-ColorOutput "   Stellar Sync Container Update   " "Cyan"
Write-ColorOutput "====================================" "Cyan"
Write-Host

try {
    # Step 1: Stop the container
    Write-ColorOutput "Step 1: Stopping the container..." "Yellow"
    docker-compose down
    if ($LASTEXITCODE -ne 0) { throw "Failed to stop container" }
    Write-ColorOutput "Container stopped successfully." "Green"
    Write-Host

    # Step 2: Pull the latest image
    Write-ColorOutput "Step 2: Pulling the latest image..." "Yellow"
    docker-compose pull
    if ($LASTEXITCODE -ne 0) { throw "Failed to pull latest image" }
    Write-ColorOutput "Latest image pulled successfully." "Green"
    Write-Host

    # Step 3: Start the container with the new image
    Write-ColorOutput "Step 3: Starting the container with the new image..." "Yellow"
    docker-compose up -d
    if ($LASTEXITCODE -ne 0) { throw "Failed to start container" }
    Write-ColorOutput "Container started successfully." "Green"
    Write-Host

    # Display success message
    Write-ColorOutput "Update complete! Container is now running with the latest image." "Green"
    Write-ColorOutput "You can check the logs with: docker-compose logs -f" "Cyan"

    # Get container info
    Write-ColorOutput "`nContainer Status:" "Magenta"
    docker-compose ps
}
catch {
    # Display error message
    Write-ColorOutput "Error: $_" "Red"
    Write-ColorOutput "Update failed. Please check the error message above." "Red"
    exit 1
}
